[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "arcoa-agent"
version = "0.3.0"
description = "Arcoa Nexus hardware diagnostics agent"
readme = "README.md"
requires-python = ">=3.9"
authors = [
  { name = "Arcoa" },
]
license = { text = "MIT" }
classifiers = [
  "Programming Language :: Python :: 3",
  "Operating System :: OS Independent",
]

dependencies = [
  "psutil>=5.9",
  "rich>=13.0",
]

[project.scripts]
arcoa-agent = "agent.cli:main"

[tool.mypy]
python_version = "3.9"
strict = true

[tool.ruff]
line-length = 100

[tool.pytest.ini_options]
addopts = "-ra"

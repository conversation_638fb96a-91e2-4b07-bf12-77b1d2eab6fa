import ast
import os
import sys

sys.path.insert(0, os.getcwd()) # Ensure agent modules are discoverable

ORCHESTRATOR_FILE_PATH = "agent/core/test_orchestrator.py"

TYPE_A_TEST_PATHS = [
    "agent.tests.battery_test.run_battery_test",
    "agent.tests.battery_test.run_battery_discharge_test",
    "agent.tests.battery_test.run_battery_charge_test",
    "agent.tests.battery_test.run_battery_full_assessment",
]

AGENT_CORE_DIR = os.path.dirname(ORCHESTRATOR_FILE_PATH)
if not os.path.exists(AGENT_CORE_DIR):
    os.makedirs(AGENT_CORE_DIR)
    with open(os.path.join(AGENT_CORE_DIR, "__init__.py"), "w") as f: f.write("")

if not os.path.exists(ORCHESTRATOR_FILE_PATH):
    with open(ORCHESTRATOR_FILE_PATH, "w") as f_dummy_orch:
        f_dummy_orch.write("""
import tkinter as tk
from typing import Callable, Dict, Any, Optional, List
from agent.tests.profiles import Profile
from agent.core.result_manager import ResultManager

class TestOrchestrator:
    def __init__(self, log_callback: Callable, result_manager_instance: ResultManager,
                 main_app_ref: tk.Tk, get_current_profile_callback: Callable,
                 get_asset_number_callback: Callable, get_operator_id_callback: Callable):
        self.log_callback = log_callback
        self.result_manager_instance = result_manager_instance
        self.main_app_ref = main_app_ref
        self.get_current_profile_callback = get_current_profile_callback
        self.get_asset_number_callback = get_asset_number_callback
        self.get_operator_id_callback = get_operator_id_callback
        self.end_screen_summary_data = {"test_results": []} # IMPORTANT: Initialized as a list

    def execute_tests(self, tests_to_run: List[str], category: str): # Original signature (example)
        self.test_function_map = {}
        test_name_simple = "dummy" # Dummy
        for test_path_in_profile in tests_to_run:
            test_func = None
            test_kwargs = {}
            if test_func:
                result = test_func(**test_kwargs)
        print("Dummy execute_tests ran")
""")
        print(f"Created dummy {ORCHESTRATOR_FILE_PATH}")
dummy_files_to_check = {
    "agent/tests/profiles.py": "class Profile: pass",
    "agent/core/result_manager.py": "class ResultManager: pass"
}
for d_path, d_content in dummy_files_to_check.items():
    d_dir = os.path.dirname(d_path)
    if not os.path.exists(d_dir): os.makedirs(d_dir)
    if not os.path.exists(os.path.join(d_dir, "__init__.py")):
        with open(os.path.join(d_dir, "__init__.py"), "w") as f: f.write("")
    if not os.path.exists(d_path):
        with open(d_path, "w") as f: f.write(d_content)
        print(f"Created dummy {d_path} for import resolution.")

class ExecuteTestsTransformer(ast.NodeTransformer):
    def __init__(self, type_a_test_list_name="TYPE_A_TEST_PATHS_QUALIFIED"):
        super().__init__()
        self.type_a_list_name = type_a_test_list_name
        self.modified_signature = False
        self.inserted_skip_logic = False
        self.modified_kwargs_logic = False

    def visit_FunctionDef(self, node):
        if node.name == 'execute_tests':
            has_headless_param = any(arg.arg == 'headless_mode' for arg in node.args.args)
            if not has_headless_param:
                node.args.args.append(ast.arg(arg='headless_mode', annotation=None))
                if len(node.args.defaults) < (len(node.args.args) - 1):
                     node.args.defaults.append(ast.Constant(value=False))
                self.modified_signature = True

            for i_body, body_item in enumerate(node.body):
                if isinstance(body_item, ast.For) and isinstance(body_item.target, ast.Name):
                    loop_var_name = body_item.target.id

                    log_msg_fstring_values = [
                        ast.Constant(value="Skipping GUI test "),
                        ast.FormattedValue(
                            value=ast.Name(id='test_name_simple', ctx=ast.Load()),
                            conversion=-1,
                            format_spec=None
                        ),
                        ast.Constant(value=" in headless mode.")
                    ]

                    # Simplified append_to_summary_node: self.end_screen_summary_data["test_results"].append({...})
                    append_to_summary_node = ast.Expr(value=ast.Call(
                        func=ast.Attribute(
                            value=ast.Subscript(
                                value=ast.Attribute(
                                    value=ast.Name(id='self', ctx=ast.Load()),
                                    attr='end_screen_summary_data',
                                    ctx=ast.Load()
                                ),
                                slice=ast.Constant(value="test_results"),
                                ctx=ast.Load()
                            ),
                            attr='append',
                            ctx=ast.Load()
                        ),
                        args=[ast.Dict(
                            keys=[ast.Constant(value='name'), ast.Constant(value='status'), ast.Constant(value='notes')],
                            values=[
                                ast.Name(id='test_name_simple', ctx=ast.Load()),
                                ast.Constant(value='SKIPPED'),
                                ast.Constant(value='Skipped in headless mode')
                            ]
                        )],
                        keywords=[]
                    ))

                    skip_if_node = ast.If(
                        test=ast.BoolOp(
                            op=ast.And(),
                            values=[
                                ast.Name(id='headless_mode', ctx=ast.Load()),
                                ast.Compare(
                                    left=ast.Name(id=loop_var_name, ctx=ast.Load()),
                                    ops=[ast.NotIn()],
                                    comparators=[ast.Name(id=self.type_a_list_name, ctx=ast.Load())]
                                )
                            ]
                        ),
                        body=[
                            ast.Expr(value=ast.Call(
                                func=ast.Attribute(value=ast.Name(id='self', ctx=ast.Load()), attr='log_callback', ctx=ast.Load()),
                                args=[ast.JoinedStr(values=log_msg_fstring_values), ast.Constant(value="warning")],
                                keywords=[]
                            )),
                            append_to_summary_node,
                            ast.Continue()
                        ],
                        orelse=[]
                    )
                    body_item.body.insert(0, skip_if_node)
                    self.inserted_skip_logic = True

                    for j_stmt, stmt_in_loop in enumerate(body_item.body):
                        if isinstance(stmt_in_loop, ast.Assign) and \
                           stmt_in_loop.value and isinstance(stmt_in_loop.value, ast.Call) and \
                           isinstance(stmt_in_loop.value.func, ast.Name) and \
                           stmt_in_loop.value.func.id == 'test_func':

                            copy_kwargs_assign = ast.Assign(
                                targets=[ast.Name(id='final_test_kwargs', ctx=ast.Store())],
                                value=ast.Call(func=ast.Name(id='dict', ctx=ast.Load()),
                                               args=[ast.Name(id='test_kwargs', ctx=ast.Load())], keywords=[])
                            )

                            conditional_parent_none_if = ast.If(
                                test=ast.BoolOp(
                                    op=ast.And(),
                                    values=[
                                        ast.Name(id='headless_mode', ctx=ast.Load()),
                                        ast.Compare(
                                            left=ast.Name(id=loop_var_name, ctx=ast.Load()),
                                            ops=[ast.In()],
                                            comparators=[ast.Name(id=self.type_a_list_name, ctx=ast.Load())]
                                        )
                                    ]
                                ),
                                body=[
                                    ast.If(
                                        test=ast.Compare(left=ast.Constant(value='parent_window'),
                                                        ops=[ast.In()],
                                                        comparators=[ast.Name(id='final_test_kwargs', ctx=ast.Load())]),
                                        body=[ast.Assign(
                                            targets=[ast.Subscript(
                                                value=ast.Name(id='final_test_kwargs', ctx=ast.Load()),
                                                slice=ast.Constant(value='parent_window'),
                                                ctx=ast.Store())],
                                            value=ast.Constant(value=None))],
                                        orelse=[]
                                    )
                                ],
                                orelse=[]
                            )

                            stmt_in_loop.value.args = []
                            stmt_in_loop.value.keywords = [ast.keyword(arg=None, value=ast.Name(id='final_test_kwargs', ctx=ast.Load()))]

                            body_item.body.insert(j_stmt, copy_kwargs_assign)
                            body_item.body.insert(j_stmt + 1, conditional_parent_none_if)
                            self.modified_kwargs_logic = True
                            break
                    break
        return node

with open(ORCHESTRATOR_FILE_PATH, "r") as source_file:
    source_code = source_file.read()
tree = ast.parse(source_code)

type_a_list_ast = ast.List(
    elts=[ast.Constant(value=p) for p in TYPE_A_TEST_PATHS],
    ctx=ast.Load()
)
type_a_assign_ast = ast.Assign(
    targets=[ast.Name(id='TYPE_A_TEST_PATHS_QUALIFIED', ctx=ast.Store())],
    value=type_a_list_ast
)

insert_pos = 0
if tree.body and isinstance(tree.body[0], ast.Expr) and isinstance(tree.body[0].value, (ast.Str, ast.Constant)):
    insert_pos = 1
for i, node in enumerate(tree.body[insert_pos:], insert_pos):
    if not isinstance(node, (ast.Import, ast.ImportFrom)):
        insert_pos = i
        break
else:
    insert_pos = len(tree.body)
tree.body.insert(insert_pos, type_a_assign_ast)
print(f"Inserted TYPE_A_TEST_PATHS_QUALIFIED global constant.")

transformer = ExecuteTestsTransformer()
modified_tree = transformer.visit(tree)
ast.fix_missing_locations(modified_tree)

if not transformer.modified_signature: print("Warning: execute_tests signature not modified.")
if not transformer.inserted_skip_logic: print("Warning: Skip logic for GUI tests not inserted.")
if not transformer.modified_kwargs_logic: print("Warning: Kwargs modification logic for test_func call not inserted/applied.")

try:
    modified_source_code = ast.unparse(modified_tree)
except AttributeError as e:
    print(f"Unparse error: {e}") # Print the specific unparse error
    try:
        import astor
        modified_source_code = astor.to_source(modified_tree)
        print("Used astor for unparsing.")
    except ImportError:
        print("Error: astor is not installed. Python 3.9+ or astor is required.")
        exit(1)
    except Exception as ae: # Catch potential astor errors too
        print(f"Astor error: {ae}")
        exit(1)


with open(ORCHESTRATOR_FILE_PATH, "w") as source_file:
    source_file.write(modified_source_code)

print(f"Refactoring {ORCHESTRATOR_FILE_PATH} for headless mode attempted.")
if transformer.modified_signature: print("Added 'headless_mode' parameter to execute_tests.")
if transformer.inserted_skip_logic: print("execute_tests will now attempt to skip Type B tests in headless_mode.")
if transformer.modified_kwargs_logic: print("parent_window argument handling adjusted for Type A tests in headless_mode.")

print("Script finished.")
EOF

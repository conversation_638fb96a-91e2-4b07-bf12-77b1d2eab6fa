import ast
import os
import sys

# Ensure agent modules are discoverable
sys.path.insert(0, os.getcwd())

# --- Dummy TestCategory and get_available_tests in case of import failure ---
class DummyTestCategory:
    OTHER = "other"
    CPU = "cpu"
    VISUAL = "visual"
    # Add other categories as they become known or needed by dummy tests

def dummy_get_available_tests():
    print("Warning: Using DUMMY get_available_tests() due to import error or missing module.")
    # Create dummy agent/tests directory and a dummy test file if they don't exist
    dummy_test_module_dir = os.path.join("agent", "tests")
    if not os.path.exists(dummy_test_module_dir):
        os.makedirs(dummy_test_module_dir)
        with open(os.path.join(dummy_test_module_dir, "__init__.py"), "w") as f:
            f.write("# Dummy init for agent.tests\n")
        print(f"Created dummy directory and __init__.py for {dummy_test_module_dir}")

    dummy_test_file_path = os.path.join(dummy_test_module_dir, "dummy_test.py")
    if not os.path.exists(dummy_test_file_path):
        with open(dummy_test_file_path, "w") as f_dummy_test:
            f_dummy_test.write("""
# Dummy test file: agent/tests/dummy_test.py
# from agent.tests.test_framework import TestCategory # Assuming this would be the real import

# For dummy, we define TestCategory locally or rely on the one in list_tests.py
class TestCategory: # Local dummy if needed here
    OTHER = "other"
    CPU = "cpu"
    VISUAL = "visual"

def run_dummy_visual_test(): pass
def run_dummy_headless_test(): pass
""")
        print(f"Created dummy test file: {dummy_test_file_path}")

    return [
        {"qualified_name": "agent.tests.dummy_test.run_dummy_visual_test", "name": "Dummy Visual Test", "description": "A dummy visual test.", "category": DummyTestCategory.VISUAL},
        {"qualified_name": "agent.tests.dummy_test.run_dummy_headless_test", "name": "Dummy Headless Test", "description": "A dummy headless test.", "category": DummyTestCategory.CPU},
    ]

# Attempt to import the real TestCategory and get_available_tests
try:
    from agent.tests.test_framework import get_available_tests, TestCategory
    print("Successfully imported from agent.tests.test_framework.")
except ImportError as e:
    print(f"Error importing from agent.tests.test_framework: {e}. Using dummy implementations.")
    # Ensure agent.tests.test_framework.py exists with dummy content if it's missing,
    # so that other parts of the system that might import it don't completely fail.
    test_framework_path = os.path.join("agent", "tests", "test_framework.py")
    if not os.path.exists(test_framework_path):
        if not os.path.exists(os.path.dirname(test_framework_path)):
            os.makedirs(os.path.dirname(test_framework_path))
            with open(os.path.join(os.path.dirname(test_framework_path), "__init__.py"), "w") as f:
                 f.write("") # Make agent.tests a package
        with open(test_framework_path, "w") as f_tf_dummy:
            f_tf_dummy.write("""
# Dummy agent.tests.test_framework.py
class TestCategory:
    OTHER = "other"
    CPU = "cpu"
    VISUAL = "visual"
    # Add other categories as needed

def get_available_tests():
    # This dummy in test_framework.py itself could return a basic test
    # or rely on the list_tests.py's more comprehensive dummy.
    # For now, let it be minimal, as list_tests.py has the primary fallback.
    return []
print(f"Created dummy {test_framework_path} as it was missing.")
""")
    TestCategory = DummyTestCategory
    get_available_tests = dummy_get_available_tests


all_tests = get_available_tests()

print("\n--- Available Tests ---")
if not all_tests:
    print("No tests found by get_available_tests().")
else:
    for test_info in all_tests:
        q_name = test_info.get("qualified_name", "N/A")
        name = test_info.get("name", "N/A")
        # Ensure category is a string, not an enum instance, for consistent printing
        category_val = test_info.get("category")
        if hasattr(category_val, 'value'): # If it's an Enum instance
             category_str = str(category_val.value)
        else: # If it's already a string or other primitive
             category_str = str(category_val if category_val is not None else TestCategory.OTHER) # Default if None

        module_path_parts = q_name.split('.')[:-1]
        module_file_path = os.path.join(*module_path_parts) + ".py"

        print(f"Name: {name}")
        print(f"  Qualified Name: {q_name}")
        print(f"  Category: {category_str}") # Print string representation
        print(f"  Module File: {module_file_path}")
        print("-" * 20)

print(f"Total tests found: {len(all_tests)}")

# Also, list the contents of agent/tests/ to see all files
print("\n--- Files in agent/tests/ directory ---")
agent_tests_dir_path = os.path.join("agent", "tests")
try:
    if not os.path.exists(agent_tests_dir_path):
        print(f"Directory {agent_tests_dir_path} not found.")
    else:
        test_dir_contents = os.listdir(agent_tests_dir_path)
        if not test_dir_contents:
            print(f"Directory {agent_tests_dir_path} is empty.")
        else:
            for item in test_dir_contents:
                print(item)
except Exception as e:
    print(f"Error listing {agent_tests_dir_path} directory: {e}")

print("\nScript list_tests.py finished.")
EOF

import ast
import os
import sys

APP_FILE_PATH = "web_server/app.py"

# Ensure 'agent' directory is discoverable for imports.
sys.path.insert(0, os.getcwd())

# --- Create dummy versions of dependencies if they don't exist ---
dummy_files_to_ensure = {
    "agent/core/test_orchestrator.py": "class TestOrchestrator: pass",
    "agent/core/result_manager.py": "class ResultManager: pass",
    "agent/tests/profiles.py": "class Profile: pass\ndef load_profile(name): return None"
}
for path, content in dummy_files_to_ensure.items():
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
        with open(os.path.join(dir_name, "__init__.py"), "w") as f: f.write("") # Ensure package
    if not os.path.exists(path):
        with open(path, "w") as f_dummy:
            f_dummy.write(content)
        print(f"Created dummy file: {path}")

# New route and necessary imports as a string block
run_tests_endpoint_string = '''
import logging # For server_logger = logging.getLogger(...)
from agent.core.test_orchestrator import TestOrchestrator
from agent.core.result_manager import ResultManager
from agent.tests.profiles import load_profile, Profile # Profile for type hint if orchestrator needs it

server_logger = logging.getLogger('web_server')
# Basic config for the logger if no handlers are set for it
if not server_logger.hasHandlers():
    # Attempt to get Flask app's logger if available, else basicConfig
    # This part is tricky as 'app' instance might not be available globally here.
    # For now, let's assume basicConfig or that Flask app setup handles logger.
    # A better place for logger config is main app setup.
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # Simplification: Rely on Flask's default logger or separate config.
    # If 'app.logger' is used, it would be 'app.logger.info(...)'
    # Sticking to 'server_logger' as defined.
    pass


def headless_log_callback(message, level="info"):
    if level == "error":
        server_logger.error(f"[TestOrchestrator] {message}")
    elif level == "warning":
        server_logger.warning(f"[TestOrchestrator] {message}")
    else:
        server_logger.info(f"[TestOrchestrator] {message}")

@app.route('/api/run_tests', methods=['POST'])
def run_tests_route():
    # request, jsonify are assumed to be imported from flask in the main app.py
    if not request.json:
        return jsonify({"error": "Invalid input, JSON required"}), 400

    data = request.json
    asset_number = data.get('asset_number')
    operator_id = data.get('operator_id')
    profile_name = data.get('profile_name')

    if not all([asset_number, operator_id, profile_name]):
        return jsonify({"error": "Missing required fields: asset_number, operator_id, profile_name"}), 400

    profile_to_run = load_profile(profile_name)
    if not profile_to_run:
        return jsonify({"error": f"Profile '{profile_name}' not found"}), 404

    try:
        result_manager = ResultManager() # Refactored to take no args

        orchestrator = TestOrchestrator(
            log_callback=headless_log_callback,
            result_manager_instance=result_manager,
            main_app_ref=None,
            get_current_profile_callback=lambda: profile_to_run,
            get_asset_number_callback=lambda: asset_number,
            get_operator_id_callback=lambda: operator_id
        )

        # Assuming execute_tests now handles its own threading or is fine being blocking.
        # The first argument to execute_tests is 'tests_to_run' (list of test paths)
        # which comes from profile_to_run.tests.
        # The second is 'category', which is not used by the refactored orchestrator's execute_tests logic
        # if it directly uses the profile's tests.
        # The refactored execute_tests should take (headless_mode=Boolean).
        # It internally gets tests from get_current_profile_callback().tests

        # orchestrator.execute_tests(tests_to_run=profile_to_run.tests, category="all", headless_mode=True)
        # Simpler call if execute_tests internally uses get_current_profile_callback:
        orchestrator.execute_tests(headless_mode=True)

        # Placeholder for actual summary data; TestOrchestrator might need a method for this
        # if self.end_screen_summary_data is not directly accessible or suitable.
        # For now, a simple success message.

        return jsonify({
            "message": f"Headless test execution for profile '{profile_name}' on asset '{asset_number}' completed or started.",
        }), 200

    except Exception as e:
        server_logger.error(f"Error during /api/run_tests for asset {asset_number}, profile {profile_name}: {e!s}", exc_info=True)
        return jsonify({"error": "Failed to run tests due to an internal server error", "details": str(e)}), 500
'''

# --- Read existing app code ---
with open(APP_FILE_PATH, "r") as source_file:
    source_code = source_file.read()
tree = ast.parse(source_code)

# --- Parse new routes and helper functions ---
new_code_module = ast.parse(run_tests_endpoint_string)

# --- Separate imports from definitions in the new code ---
new_imports = [n for n in new_code_module.body if isinstance(n, (ast.Import, ast.ImportFrom))]
new_definitions = [n for n in new_code_module.body if not isinstance(n, (ast.Import, ast.ImportFrom))]


# --- Safely add new imports ---
# Store existing imports and other nodes separately
existing_docstring = None
if tree.body and isinstance(tree.body[0], ast.Expr) and isinstance(tree.body[0].value, (ast.Str, ast.Constant)):
    existing_docstring = tree.body[0]
    processing_nodes = tree.body[1:]
else:
    processing_nodes = tree.body

existing_imports_nodes = [n for n in processing_nodes if isinstance(n, (ast.Import, ast.ImportFrom))]
other_body_nodes = [n for n in processing_nodes if not isinstance(n, (ast.Import, ast.ImportFrom))]

final_imports_section = list(existing_imports_nodes)
existing_imports_dumps = {ast.dump(imp) for imp in final_imports_section}

for new_imp_node in new_imports:
    if ast.dump(new_imp_node) not in existing_imports_dumps:
        final_imports_section.append(new_imp_node)
        existing_imports_dumps.add(ast.dump(new_imp_node))
        print(f"Adding new import: {ast.unparse(new_imp_node).strip()}")

# Reconstruct body: docstring, imports, then other existing nodes
new_tree_body = []
if existing_docstring:
    new_tree_body.append(existing_docstring)
new_tree_body.extend(final_imports_section)
new_tree_body.extend(other_body_nodes)
tree.body = new_tree_body


# --- Add helper function (headless_log_callback) and the route function definitions ---
# Add them before the 'if __name__ == "__main__":' block if it exists, or at the end.
main_block_index = -1
for i, node in enumerate(tree.body):
    if isinstance(node, ast.If) and isinstance(node.test, ast.Compare):
        if isinstance(node.test.left, ast.Name) and node.test.left.id == '__name__':
            if isinstance(node.test.ops[0], ast.Eq) and \
               isinstance(node.test.comparators[0], ast.Constant) and \
               node.test.comparators[0].value == '__main__':
                main_block_index = i
                break

# Add new definitions (logger setup, callback, route)
# We need to ensure the logger setup `server_logger = ...` is at the module level,
# not inside a function.
added_definitions_names = set()

for new_def_node in new_definitions:
    node_name = getattr(new_def_node, 'name', None) # For functions/classes
    if isinstance(new_def_node, ast.Assign): # For server_logger = ...
        if isinstance(new_def_node.targets[0], ast.Name):
            node_name = new_def_node.targets[0].id

    is_duplicate = False
    if node_name:
        for existing_node in tree.body:
            existing_name = getattr(existing_node, 'name', None)
            if isinstance(existing_node, ast.Assign) and isinstance(existing_node.targets[0], ast.Name):
                existing_name = existing_node.targets[0].id
            if node_name == existing_name:
                is_duplicate = True
                print(f"Note: Definition for '{node_name}' already exists. Skipping re-addition.")
                break
    if not is_duplicate:
        if main_block_index != -1:
            tree.body.insert(main_block_index, new_def_node)
            main_block_index +=1 # Adjust index for next insertion
        else:
            tree.body.append(new_def_node)
        if node_name: added_definitions_names.add(node_name)

print(f"Added definitions for: {added_definitions_names}")


# --- Convert AST back to source and write ---
ast.fix_missing_locations(tree)
try:
    modified_source_code = ast.unparse(tree)
except AttributeError:
    try:
        import astor
        modified_source_code = astor.to_source(tree)
        print("Used astor for unparsing.")
    except ImportError:
        print("Error: ast.unparse is not available and astor is not installed.")
        exit(1)

with open(APP_FILE_PATH, "w") as source_file:
    source_file.write(modified_source_code)

print(f"Endpoint /api/run_tests and helpers added to {APP_FILE_PATH}.")
print("Script finished.")
EOF

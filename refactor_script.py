import ast
import os
import sys
sys.path.insert(0, os.getcwd())

RESULT_MANAGER_FILE_PATH = 'agent/core/result_manager.py'

# Create dummy agent/core/result_manager.py if it doesn't exist
AGENT_CORE_DIR = os.path.dirname(RESULT_MANAGER_FILE_PATH)
if not os.path.exists(AGENT_CORE_DIR):
    os.makedirs(AGENT_CORE_DIR)
    # Create __init__.py if core is a new directory
    with open(os.path.join(AGENT_CORE_DIR, "__init__.py"), "w") as f_init:
        f_init.write("")

if not os.path.exists(RESULT_MANAGER_FILE_PATH):
    with open(RESULT_MANAGER_FILE_PATH, "w") as f_dummy:
        f_dummy.write("""
import logging
import json
import time
import os
from typing import List, Dict, Any, Optional, Callable

class ResultManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # Basic config if no handlers are present
        if not self.logger.handlers:
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.test_results_available: bool = False # Indicates if any results have been successfully saved

    def save_result_to_file(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any]) -> bool:
        # Dummy implementation
        self.logger.info(f"Dummy save_result_to_file called for {test_name}")
        return True

    def add_result(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any]):
        # Dummy implementation
        self.logger.info(f"Dummy add_result called for {test_name}")
        if self.save_result_to_file(asset_number, operator_id, test_name, result):
            self.test_results_available = True

    def _load_and_parse_results(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        self.logger.info("Dummy _load_and_parse_results called")
        return []

    def consolidate_results_for_asset(self, asset_number: str, operator_id: str) -> Optional[str]:
        self.logger.info("Dummy consolidate_results_for_asset called")
        return None
""")
    print(f"Created dummy {RESULT_MANAGER_FILE_PATH}")


# New __init__
new_init_string = '''
def __init__(self):
    self.logger = logging.getLogger(__name__)
    if not self.logger.handlers:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    self.test_results_available: bool = False
'''

# New save_result_to_file
new_save_result_string = '''
def save_result_to_file(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any]) -> bool:
    try:
        timestamp = int(time.time())
        results_dir = 'results'
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        asset_number = asset_number or 'unknown_asset'
        operator_id = operator_id or 'unknown_operator'
        safe_test_name = test_name.replace(' ', '_').replace('/', '_').replace('\\\\', '_')
        filename = f'{results_dir}/nexus_result_{asset_number}_{safe_test_name}_{timestamp}.json'
        save_data = {
            'asset_number': asset_number,
            'operator_id': operator_id,
            'test_name': test_name,
            'timestamp': timestamp,
            'result': result
        }
        with open(filename, 'w') as f:
            json.dump(save_data, f, indent=2)
        self.logger.info(f'Test result saved to {filename}')
        return True
    except Exception as e:
        self.logger.error(f'Error saving test result to file: {str(e)}')
        return False
'''

# New add_result
new_add_result_string = '''
def add_result(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any]):
    if 'test_details' in result:
        status = result.get('test_details', {}).get('status', 'unknown')
        notes = result.get('test_details', {}).get('notes', '')
        self.logger.info(f'Test result for {test_name}: {status}')
        if notes:
            self.logger.info(f'Notes: {notes}')
    elif 'status' in result:
        status = result.get('status', 'unknown')
        grade = result.get('grade', 'N/A')
        notes = result.get('notes', '')
        log_level_map = {'pass': 'info', 'fail': 'error', 'warning': 'warning'}
        effective_log_level = log_level_map.get(status.lower(), 'info') # ensure status is lower for map lookup
        msg = f'Test result for {test_name}: {status.upper()} (Grade: {grade})'
        if effective_log_level == 'error': self.logger.error(msg)
        elif effective_log_level == 'warning': self.logger.warning(msg)
        else: self.logger.info(msg)
        if notes:
            details_msg = f'Details for {test_name}: ' + ('; '.join(notes) if isinstance(notes, list) else str(notes))
            self.logger.info(details_msg)
        metrics = result.get('metrics', {})
        if metrics:
            self.logger.info(f'Metrics for {test_name}:')
            for key, value in metrics.items():
                if isinstance(value, (list, dict)): continue
                self.logger.info(f'  - {key}: {value}')
    else:
        self.logger.info(f'Test result for {test_name} (unknown format): {str(result)[:200]}')
    if self.save_result_to_file(asset_number, operator_id, test_name, result):
        self.test_results_available = True
    else:
        self.logger.warning(f'Failed to save result for {test_name}. Results availability not changed.')
'''

# New _load_and_parse_results (Full definition with logging changes)
new_load_parse_string = '''
def _load_and_parse_results(self, file_paths: List[str]) -> List[Dict[str, Any]]:
    parsed_results = []
    for file_path in file_paths:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                # Attempt to repair common JSON issues if needed, for now direct load
                data = json.loads(content)
            # Basic validation of loaded data structure (optional)
            if not isinstance(data, dict) or not all(k in data for k in ['asset_number', 'operator_id', 'test_name', 'timestamp', 'result']):
                self.logger.warning(f"Result file {file_path} has unexpected structure.")
                # Decide whether to append malformed data or skip
                # parsed_results.append({'error': 'Malformed data', 'filepath': file_path, **data})
                continue # Skip malformed data for now

            parsed_results.append(data)
            self.logger.debug(f'Successfully parsed result file: {file_path}')
        except FileNotFoundError:
            self.logger.error(f'Result file not found: {file_path}')
        except json.JSONDecodeError as e:
            self.logger.error(f'Error decoding JSON from result file {file_path}: {e}')
        except Exception as e:
            self.logger.error(f'Unexpected error parsing result file {file_path}: {e}')
    return parsed_results
'''

# New consolidate_results_for_asset (Full definition with logging changes)
new_consolidate_string = '''
def consolidate_results_for_asset(self, asset_number: str, operator_id: str) -> Optional[str]:
    results_dir = 'results'
    if not os.path.exists(results_dir):
        self.logger.warning(f"Results directory '{results_dir}' not found. No results to consolidate for asset {asset_number}.")
        return None

    asset_files = []
    for filename in os.listdir(results_dir):
        # Example filename: nexus_result_ASSET123_test_power_on_1678886400.json
        # Refine filename parsing if operator_id is also in filename or needed for stricter matching
        if filename.startswith(f"nexus_result_{asset_number}_") and filename.endswith(".json"):
            asset_files.append(os.path.join(results_dir, filename))

    if not asset_files:
        self.logger.info(f"No individual result files found for asset {asset_number} in {results_dir}.")
        return None

    self.logger.debug(f"Found {len(asset_files)} result files for asset {asset_number}.")

    all_results_for_asset = self._load_and_parse_results(asset_files)
    if not all_results_for_asset:
        self.logger.warning(f"Could not parse any valid results for asset {asset_number} from found files.")
        return None

    # Sort results by timestamp for chronological order in consolidated file
    all_results_for_asset.sort(key=lambda x: x.get('timestamp', 0))

    consolidated_data = {
        'asset_number': asset_number,
        'operator_id': operator_id, # Use provided operator_id for the consolidation event
        'consolidation_timestamp': int(time.time()),
        'individual_results_count': len(all_results_for_asset),
        'results': all_results_for_asset # Array of individual result structures
    }

    consolidated_filename = os.path.join(results_dir, f"consolidated_nexus_results_{asset_number}.json")
    try:
        with open(consolidated_filename, 'w') as f:
            json.dump(consolidated_data, f, indent=2)
        self.logger.info(f"Consolidated results for asset {asset_number} saved to {consolidated_filename}")
        # Optionally, archive or delete individual files after consolidation
        # for f_path in asset_files: os.remove(f_path)
        # self.logger.info(f"Removed {len(asset_files)} individual result files for asset {asset_number}.")
        return consolidated_filename
    except Exception as e:
        self.logger.error(f"Failed to consolidate results for asset {asset_number}: {e}")
        return None
'''


# AST manipulation logic
with open(RESULT_MANAGER_FILE_PATH, 'r') as source_file:
    source_code = source_file.read()
tree = ast.parse(source_code)

def replace_or_add_method(class_node, new_method_string):
    new_method_ast = ast.parse(new_method_string).body[0]
    method_replaced = False
    for i, node in enumerate(class_node.body):
        if isinstance(node, ast.FunctionDef) and node.name == new_method_ast.name:
            class_node.body[i] = new_method_ast
            method_replaced = True
            break
    if not method_replaced:
        class_node.body.append(new_method_ast)

class_found = False
for node_item in tree.body:
    if isinstance(node_item, ast.ClassDef) and node_item.name == 'ResultManager':
        class_found = True
        replace_or_add_method(node_item, new_init_string)
        replace_or_add_method(node_item, new_save_result_string)
        replace_or_add_method(node_item, new_add_result_string)
        replace_or_add_method(node_item, new_load_parse_string)
        replace_or_add_method(node_item, new_consolidate_string)
        break

if not class_found:
    print(f'Error: Class ResultManager not found in {RESULT_MANAGER_FILE_PATH}')
    # Create a dummy class if not found, so the rest of the script can run for imports
    result_manager_class_ast = ast.parse("class ResultManager: pass").body[0]
    tree.body.append(result_manager_class_ast)
    # And add methods to this new class
    for node_item_new in tree.body:
        if isinstance(node_item_new, ast.ClassDef) and node_item_new.name == 'ResultManager':
            replace_or_add_method(node_item_new, new_init_string)
            replace_or_add_method(node_item_new, new_save_result_string)
            replace_or_add_method(node_item_new, new_add_result_string)
            replace_or_add_method(node_item_new, new_load_parse_string)
            replace_or_add_method(node_item_new, new_consolidate_string)
            break
    print(f"Created ResultManager class and added methods as it was not found.")


# Ensure 'logging', 'json', 'time', 'os' imports and 'List, Dict, Any, Optional' from typing
imports_to_ensure_map = {
    'logging': None,
    'json': None,
    'time': None,
    'os': None,
    'typing': ['List', 'Dict', 'Any', 'Optional'] # Removed Callable as it's not used in new methods
}
final_import_nodes = []
existing_imports_in_tree = []
other_body_nodes = []

# Separate imports from other nodes
docstring_node = None
if tree.body and isinstance(tree.body[0], ast.Expr) and isinstance(tree.body[0].value, ast.Str):
    docstring_node = tree.body[0]
    remaining_body_nodes = tree.body[1:]
else:
    remaining_body_nodes = tree.body

for node in remaining_body_nodes: # Corrected variable name from node_item to node
    if isinstance(node, (ast.Import, ast.ImportFrom)):
        existing_imports_in_tree.append(node)
    else:
        other_body_nodes.append(node)

# Start with existing imports
final_import_nodes.extend(existing_imports_in_tree)
processed_modules_for_names = set() # To track modules where we've handled specific named imports

# Ensure modules and their specific named imports
for module_name, specific_names_to_ensure in imports_to_ensure_map.items():
    module_exists_in_tree = False
    # Check if module is already imported (either directly or as 'from module import ...')
    for existing_import in final_import_nodes:
        if isinstance(existing_import, ast.Import):
            if any(alias.name == module_name for alias in existing_import.names):
                module_exists_in_tree = True
                break
        elif isinstance(existing_import, ast.ImportFrom):
            if existing_import.module == module_name:
                module_exists_in_tree = True
                # If module exists and we need specific names from it (e.g., typing)
                if specific_names_to_ensure and module_name not in processed_modules_for_names:
                    current_names_in_import = {alias.name for alias in existing_import.names}
                    for name_to_add in specific_names_to_ensure:
                        if name_to_add not in current_names_in_import:
                            existing_import.names.append(ast.alias(name=name_to_add, asname=None))
                    processed_modules_for_names.add(module_name)
                break # Found the module, no need to check further for its existence

    if not module_exists_in_tree:
        # If module is not imported at all, add a new import statement
        if specific_names_to_ensure:
            final_import_nodes.append(ast.ImportFrom(module=module_name, names=[ast.alias(name=n, asname=None) for n in specific_names_to_ensure], level=0))
            processed_modules_for_names.add(module_name) # Mark as processed
        else:
            final_import_nodes.append(ast.Import(names=[ast.alias(name=module_name, asname=None)]))

# Reconstruct tree.body: docstring (if any), then imports, then other nodes
new_tree_body = []
if docstring_node:
    new_tree_body.append(docstring_node)
new_tree_body.extend(final_import_nodes)
new_tree_body.extend(other_body_nodes)
tree.body = new_tree_body


try:
    modified_source_code = ast.unparse(tree)
except AttributeError:
    try:
        import astor
        modified_source_code = astor.to_source(tree)
    except ImportError:
        print("Error: ast.unparse is not available and astor is not installed. Python 3.9+ or astor is required.")
        exit(1)

with open(RESULT_MANAGER_FILE_PATH, 'w') as source_file:
    source_file.write(modified_source_code)
print(f'Successfully refactored {RESULT_MANAGER_FILE_PATH}')

# Create 'results' directory (idempotent)
if not os.path.exists('results'):
    os.makedirs('results')
print("Ensured 'results' directory exists.")

# Verification
print(f"--- {RESULT_MANAGER_FILE_PATH} after refactoring (first 50 lines) ---")
with open(RESULT_MANAGER_FILE_PATH, 'r') as f:
    for i in range(50):
        line = f.readline()
        if not line: break
        print(line, end='')
print("\n--- End head ---")

print(f"--- {RESULT_MANAGER_FILE_PATH} after refactoring (add_result method) ---")
with open(RESULT_MANAGER_FILE_PATH, 'r') as f:
    in_method = False
    method_content_lines = 0
    for line in f:
        if "def add_result" in line:
            in_method = True
        if in_method:
            print(line, end='')
            method_content_lines +=1
            # Heuristic to limit lines printed for the method
            if method_content_lines > 25 and "def " in line and "add_result" not in line: # Next method start
                 break
            if line.strip() == "" and method_content_lines > 1: # Likely end of method if blank line after some content
                # Peek next line to be more sure
                current_pos = f.tell()
                next_line_peek = f.readline()
                f.seek(current_pos) # Go back
                if not next_line_peek.strip(): # if next line is also blank
                    break
                if "def " in next_line_peek: # if next line starts a new method
                    break


print("\n--- End add_result ---")

print("Refactoring script finished.")

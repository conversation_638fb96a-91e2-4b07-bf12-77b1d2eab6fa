Below is a practical “road-map” you can follow. It assumes you already have (1) a server on the same network as the machines under test and (2) switches/DHCP/PXE infrastructure that works today with EPS.

────────────────────────────────────────

Nail Down the Exact Scope ──────────────────────────────────────── ✓ What must the system do? • Hardware tests: CPU stress, RAM, keyboard, touchpad / touchscreen, LCD, battery, fans, audio, NIC/Wi-Fi, webcam, etc.
• Drive discovery, SMART health, and secure erase compliant with NIST 800-88 (<PERSON><PERSON><PERSON> was doing this).
• Barcode/serial capture + asset metadata → searchable database.
• Certificate / log export (PDF / CSV / JSON) per asset or bulk.
✓ What is “nice to have”? • Remote start/stop of tests from the server UI.
• Ability to run from USB/SSD (“no-PXE” fallback).
• Multithreaded test queue / pool to handle hundreds of clients simultaneously.
• Integration with your inventory / CRM.

Create a written Requirements sheet first—future-you will be thankful.

──────────────────────────────────────── 2. High-Level Architecture ──────────────────────────────────────── CLIENT SIDE
• A small Linux live environment ( ≈ 200–400 MB) that:
– boots via PXE OR from USB/ISO, no disk install needed.
– auto-runs a “test agent” (Python or Go binary) that talks to the server with HTTPS REST calls.
– bundles open-source test tools as plug-ins.

SERVER SIDE
• REST API + Web UI (FastAPI + React, or Flask + Vue, etc.).
• PostgreSQL or MariaDB for asset/test/wipe records.
• Object storage (local or S3-compatible) for full log files.
• Optional background workers (Celery / RQ) for PDF generation, email, etc.
• Authentication & role-based access (technician vs supervisor).

Network diagram:

CopyInsert
┌─────────────┐      HTTPS      ┌─────────────┐
│ Client node │◀──────────────▶│  API+DB     │
│ (PXE/USB)   │                │  Server PC  │
└─────────────┘                └─────────────┘
──────────────────────────────────────── 3. Choosing Tools & Libraries ──────────────────────────────────────── • Hardware info: lshw, dmidecode, lsblk, smartctl, ethtool, lspci, lsusb.
• CPU stress: stress-ng or sysbench.
• RAM: memtester (in-OS) + call out to memtest86+ optionally.
• Keyboard / touch / LCD: write small Python utilities using evdev, libinput, or curses-based prompts; log pass/fail on key press.
• Secure erase: nwipe (open-source fork of DBAN) or scripted sg_format --sanitize, blkdiscard, or dd if=/dev/zero. Log hash/pattern verification.
• Packaging the live OS:
– Debian Live / Ubuntu “casper” rebuild,
– TinyCore + cpio overlay, or
– Buildroot (smallest, but more work).
• Documentation generator: WeasyPrint for PDFs, or pass data to a Node/React service that renders PDF.

──────────────────────────────────────── 4. Minimum Viable Prototype (2–4 weeks) ────────────────────────────────────────

Set up a VM to represent a “client”.
Build a Debian-based live ISO with just SSH + Python + curl.
Write agent.py that:
a. Collects serial/asset tag (dmidecode -s system-serial-number).
b. POSTs to https://yourserver/api/start.
c. Runs a 5-minute stress-ng --cpu 4 test, pipes exit code & temps to JSON.
d. POSTs results to /api/result.
On the server, spin up a bare-bones FastAPI app with two routes: /start and /result saving JSON into SQLite. Show a simple HTML table.
PXE-boot the VM → confirm you see an entry appear.
If that works, you have the “plumbing”; now iterate by adding more test modules and fleshing out the UI.

──────────────────────────────────────── 5. Making It Work Without PXE ──────────────────────────────────────── • The live image you already created for PXE can simply be written to USB drives (dd or Rufus).
• On Windows-running assets: ship a small “launcher” that reboots into the USB ISO automatically or, if you must stay in-OS, run a restricted test subset (keyboard, screen, S.M.A.R.T., no low-level wipe).
• Advantage: same agent code path; only the boot medium differs.

──────────────────────────────────────── 6. Compliance & Validation ──────────────────────────────────────── • For every erase method implemented, store method, passes, verify hash, start/stop timestamps.
• Keep SHA-256 of first/last megabyte pre/post wipe for proof.
• Automate a PDF “certificate of destruction” with your company branding.

──────────────────────────────────────── 7. Project Management Tips ──────────────────────────────────────── • Treat each test type as a plug-in; define a JSON schema for input/output:

json
CopyInsert
  {
    "id": "keyboard",
    "version": "1.0",
    "status": "pass",
    "data": { "failed_keys": [] },
    "duration_sec": 42
  }
• Keep the agent stateless: all state lives on the server so you can re-image freely.
• Version-pin your live image (e.g., filename recycler-os-0.4.2.iso) so you know exactly which assets ran which agent version.
• Automate ISO build in CI (GitHub Actions → artifact).
• Write end-to-end tests: spin up qemu PXE client in CI and assert API receives expected payload.

──────────────────────────────────────── 8. Resource Estimates (ball-park) ──────────────────────────────────────── • 1 senior software engineer (Python/Go & DevOps)
• 1 front-end or full-stack dev for UI
• 1 part-time sysadmin to maintain PXE/DHCP and images
A basic but production-ready system can often be achieved in 3–4 months of focused work.

──────────────────────────────────────── 9. Open-Source Inspirations ──────────────────────────────────────── • phoronix-test-suite – general benchmark suite (Perl).
• stress-ng, fio, memtester – building-block CLI tools.
• nwipe – secure erase tool.
• netboot.xyz – scripts for PXE booting many ISOs (good reference).
• ansible-pxelinux roles – for generating PXE menus automatically.

──────────────────────────────────────── 10. Immediate Next Actions for You ────────────────────────────────────────

Draft the Requirements document (tests, reporting, erase methods).
Decide on language & stack (Python + FastAPI is a very productive default).
Prototype the tiny live ISO and a single REST round-trip this week.
Set up a Git repo, CI pipeline, and start modularising tests.
Create a simple Postgres schema (assets, tests, wipes, users).
Once you have the PoC loop closed (client boots → runs test → server shows result), everything else is “just features”. Feel free to reach back out when you start writing code—happy to help with concrete snippets or build scripts.
### Guide: Adding a New Test to Arcoa Nexus

This guide outlines the process for creating a new test and ensuring it's discoverable by the test framework, usable in profiles, and visible in the Profile Editor.

**Core Idea:** Test discovery is now primarily handled by Python's built-in `unittest` framework. For a test to be discovered and run, it must meet several criteria:
-   Test files must be named following the `test_*.py` pattern (e.g., `test_my_component.py`).
-   Test files must reside in the `agent/tests/` directory or one of its subdirectories (like `agent/tests/unit_tests/`).
-   Within these files, tests must be methods of a class that inherits from `unittest.TestCase`.
-   These test methods **must** be decorated with the `@test(...)` decorator from `agent.tests.test_framework`. This decorator provides essential metadata (category, severity, description, etc.) that our system uses for display and filtering, and it also standardizes result reporting.
-   While `unittest` typically requires test method names to start with `test_`, our `@test` decorator integration allows any method name to be used, as long as it's decorated. However, following the `test_` prefix convention is still good practice.

### Step 1: Create the Test File
1.  **Location**: Create a new Python file in the `agent/tests/` directory or a subdirectory like `agent/tests/unit_tests/`. This is crucial for `unittest` discovery.
2.  **Naming**: Name your file starting with `test_` (e.g., `test_cpu_features.py`). This pattern is how `unittest` identifies potential test files.

### Step 2: Define the Test Class and Method(s)
1.  **Import `unittest` and `@test`**:
    ```python
    import unittest
    from agent.tests.test_framework import test, TestCategory, TestSeverity
    ```
2.  **Create a Test Class**: Define a class that inherits from `unittest.TestCase`.
    ```python
    class TestMyFeature(unittest.TestCase):
        # Your test methods will go here
        pass
    ```
3.  **Define Test Methods**: Inside your class, define methods for your individual test cases.
    *   Each method representing a test **must** be decorated with `@test(...)`.
    *   It's good practice, though not strictly required by our framework (due to the `@test` decorator), for method names to start with `test_`.
    *   The first argument to these methods will be `self` (the TestCase instance).
    *   Your test methods can accept `**kwargs` if they need to receive custom arguments (e.g., `log_callback`, or arguments defined in `test_config.py`). The `@test` decorator handles making these available.
    ```python
    class TestMyFeature(unittest.TestCase):
        @test(category=TestCategory.CPU, severity=TestSeverity.HIGH, name="Specific CPU Check")
        def test_specific_cpu_feature(self, **kwargs):
            log = kwargs.get('log_callback', print) # Use print as a fallback logger
            log("Starting the specific CPU feature test.")
            # Test logic here...
            self.assertTrue(True, "The CPU feature is working.")
            # Return value will be processed by @test decorator
            # Or, directly return TestStatus and notes:
            # return TestStatus.PASS, "Feature seems OK."
    ```

### Step 3: Use the `@test` Decorator (Details)
The `@test` decorator is vital. It integrates your `unittest.TestCase` methods with our framework's metadata system and standardized result reporting.
1.  **Import (as shown in Step 2)**: `from agent.tests.test_framework import test, TestCategory, TestSeverity, TestStatus`.
2.  **Apply**: Place `@test(...)` directly above your test method definition within the `unittest.TestCase` class.
    *   `category` (Required): `TestCategory` enum member. Add to enum in `test_framework.py` if new category needed.
    *   `severity` (Optional): `TestSeverity` enum member. Defaults to `MEDIUM`.
    *   `description` (Optional): String description. Defaults to docstring.
    *   `name` (Optional): User-friendly name for UI. Defaults to function name.
    *   `timeout` (Optional): Integer in seconds.

### Step 4: Implement Test Logic
Write the code that performs the test.

### Step 5: Return Test Results
The `@test` decorator standardizes result reporting. Your test method can return results in several ways:
1.  **Boolean**: `True` for `TestStatus.PASS`, `False` for `TestStatus.FAIL`.
    ```python
    self.assertTrue(condition) # unittest assertion
    return True # Or simply let assertions handle failure
    ```
2.  **Tuple**: `(TestStatus, "notes string")`.
    ```python
    return TestStatus.PASS, "All checks passed."
    ```
3.  **Dictionary**: `{"status": TestStatus.PASS, "notes": "...", ...}`. This allows for richer details.
    ```python
    return {"status": TestStatus.PASS, "notes": "Specific checks done.", "details_key": "value"}
    ```
4.  **`unittest` Assertions**: If a `unittest.TestCase` assertion like `self.assertEqual()` or `self.assertTrue()` fails, `unittest` raises an `AssertionError`. The `@test` decorator and `TestRunner` will catch this and report it as a `TestStatus.FAIL` or `TestStatus.ERROR` as appropriate.

### Step 6: Logging (Optional)
Test methods decorated with `@test` can receive a `log_callback` function via `**kwargs`.
```python
    def test_logging_example(self, **kwargs):
        log = kwargs.get('log_callback')
        if log:
            log("This is an info message for the test log.", "info")
            log("This is a warning.", "warning")
```
If `log_callback` is not provided by the runner (e.g. during direct `unittest` execution without our `TestRunner`), you might want a fallback like `log = kwargs.get('log_callback', print)`.

### Step 7: GUI Interaction (If Applicable)
If your test interacts with a GUI:
1.  Accept `parent_window = kwargs.get('parent_window')` in your test method.
2.  Manage the GUI lifecycle appropriately within the test function.
This remains largely unchanged, but ensure your test method signature includes `**kwargs`.

### Step 7: GUI Interaction (If Applicable)
1.  Accept `parent_window = kwargs.get('parent_window')`.
2.  Manage GUI lifecycle within the test function.

### Step 8: Verification
1.  **Discovery**: Run `python agent/run_tests.py --list` from the project root. Your new test (e.g., `your_module.YourClass.test_your_method`) should appear in the list with its category and severity.
2.  **Execution**:
    *   Run your specific test: `python agent/run_tests.py --tests your_module.YourClass.test_your_method`
    *   Or run a suite that should include your test by its metadata name (if applicable).
3.  **Profile Editor**: Open the Arcoa Nexus application. Your test should be listed in the Profile Editor under its category and in "All Tests".
4.  **Profile Execution**: Add the test to a profile, save, and run the profile. Check logs and pass/fail status.

### Example Test File

Here's a basic example of a test file (`agent/tests/test_example.py`):

```python
import unittest
from agent.tests.test_framework import test, TestCategory, TestSeverity, TestStatus

class TestExampleHardware(unittest.TestCase):

    @test(category=TestCategory.SYSTEM,
          severity=TestSeverity.MEDIUM,
          name="Example System Check",
          description="A simple example test that checks a basic condition.")
    def test_system_example_check(self, **kwargs):
        """This is an example test."""
        log = kwargs.get('log_callback', print)

        log("Starting the example system check.", "info")

        # Simulate a hardware check
        hardware_component_ok = True

        self.assertTrue(hardware_component_ok, "The example hardware component was not OK.")

        # The @test decorator will handle the result based on assertions.
        # Or you can explicitly return:
        # if hardware_component_ok:
        #     return TestStatus.PASS, "Example component is fine."
        # else:
        #     return TestStatus.FAIL, "Example component failed."

    @test(category=TestCategory.INPUT, description="Another example")
    def test_another_example(self, **kwargs):
        # This test will use its method name "test_another_example" as its 'name' in metadata
        # because the 'name' parameter is not specified in @test().
        user_input_value = 5
        self.assertEqual(user_input_value, 5, "Input value was not as expected.")
        return TestStatus.PASS # Explicit return

# To make this file runnable directly with `python -m unittest agent.tests.test_example` (optional)
if __name__ == '__main__':
    unittest.main()
```

### Troubleshooting Test Discovery

If your test is not being discovered when you run `python agent/run_tests.py --list` or when trying to execute it:

-   **File Name**: Ensure your test file is named starting with `test_` (e.g., `test_my_component.py`).
-   **File Location**: The file must be in the `agent/tests/` directory or one of its subdirectories (like `agent/tests/unit_tests/`).
-   **Class Inheritance**: Your test class must inherit from `unittest.TestCase`.
-   **`@test` Decorator**: Each test method you want to be discovered by our framework **must** be decorated with `@test(...)` from `agent.tests.test_framework`. This is how category, severity, and other metadata are associated with the test.
-   **Method Name (Convention)**: While our `@test` decorator allows any method name, `unittest` itself, by default, discovers methods that start with `test_`. It's good practice to follow this convention for your test methods (e.g., `def test_check_feature_x(self, **kwargs):`). If you omit the `test_` prefix, ensure the `@test` decorator is present.
-   **Imports**: Double-check that you have correctly imported `unittest` and the necessary components from `agent.tests.test_framework`.

### Updating Enums (If Necessary)
If a new `TestCategory` or `TestSeverity` is needed, edit the enums in `agent/tests/test_framework.py`. This process remains unchanged.

from flask import Flask, jsonify, request, abort, send_from_directory, render_template, Response
from agent.hardware.system_info import get_system_info
from agent.tests.profiles import Profile, get_all_profiles, load_profile, save_profile, delete_profile, get_profile_path
import os
import threading
import time
import json
from agent.core.result_manager import ResultManager
import glob
from agent.core.device_condition_manager import load_device_conditions, save_device_conditions, DEFAULT_DEVICE_CONDITIONS_STRUCTURE
import logging
from agent.core.test_orchestrator import TestOrchestrator
from agent.tests.profiles import load_profile, Profile
RESULTS_DIR = 'results'
app = Flask(__name__, template_folder='templates', static_folder='static')

# In-memory store for live test logs. Key: asset_number, Value: list of log strings
asset_test_logs: dict[str, list[str]] = {}
# TODO: Consider a more robust solution for production (e.g., Redis, DB, or log files per asset)
# and a mechanism to clean up old logs to prevent memory exhaustion.

# In-memory store for visual test sessions. Key: session_id, Value: test progress data
visual_test_sessions: dict[str, dict] = {}
# Lock for thread-safe access to visual test sessions
visual_test_lock = threading.Lock()

@app.route('/')
def index():
    return render_template('index.html')


@app.route('/visual_test')
def visual_test():
    """Serve the visual test interface."""
    return render_template('visual_test.html')

server_logger = logging.getLogger('web_server')
# Basic logging config if not already set up by a higher-level runner
if not server_logger.hasHandlers():
    # Check if running in a context that might already configure logging (e.g. Gunicorn)
    if not app.config.get('TESTING', False) and not app.debug: # Avoid double logging in some dev/test setups
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.INFO)
    elif app.debug: # More verbose in debug
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.DEBUG)
        server_logger.debug("Debug mode logging enabled for web_server.")


@app.route('/api/run_tests', methods=['POST'])
def run_tests_route():
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    data = request.json
    asset_number = data.get('asset_number')
    operator_id = data.get('operator_id')
    profile_name = data.get('profile_name')
    if not all([asset_number, operator_id, profile_name]):
        return (jsonify({'error': 'Missing required fields: asset_number, operator_id, profile_name'}), 400)
    profile_to_run = load_profile(profile_name)
    if not profile_to_run:
        return (jsonify({'error': f"Profile '{profile_name}' not found"}), 404)

    # Prepare for log collection for this specific run
    asset_test_logs[asset_number] = []
    def current_run_log_callback(message, level='info'):
        log_entry = f"[{level.upper()}] {message}"
        asset_test_logs[asset_number].append(log_entry)
        # Also log to server console for debugging/monitoring
        if level == 'error':
            server_logger.error(f'[Orchestrator:{asset_number}] {message}')
        elif level == 'warning':
            server_logger.warning(f'[Orchestrator:{asset_number}] {message}')
        else:
            server_logger.info(f'[Orchestrator:{asset_number}] {message}')

    try:
        # ResultManager's log_callback can also use the current_run_log_callback
        # It does not take asset_number or operator_id in constructor.
        result_manager = ResultManager(log_callback=current_run_log_callback)
        orchestrator = TestOrchestrator(
            log_callback=current_run_log_callback,
            result_manager_instance=result_manager,
            main_app_ref=None, # No Tkinter app in headless
            get_current_profile_callback=lambda: profile_to_run,
            get_asset_number_callback=lambda: asset_number,
            get_operator_id_callback=lambda: operator_id
        )
        # Consider running execute_tests in a background thread if it's long-running
        # For now, keeping it synchronous as per original structure.
        orchestrator.execute_tests(headless_mode=True)
        # The 'message' can indicate that logs are available via the new endpoint
        return (jsonify({'message': f"Test execution for profile '{profile_name}' on asset '{asset_number}' initiated. Poll /api/test_log/{asset_number} for logs."}), 200)
    except Exception as e:
        server_logger.error(f'Error during /api/run_tests for asset {asset_number}, profile {profile_name}: {e!s}', exc_info=True)
        asset_test_logs[asset_number].append(f"[ERROR] Failed to run tests due to an internal server error: {str(e)}")
        return (jsonify({'error': 'Failed to run tests due to an internal server error', 'details': str(e)}), 500)

@app.route('/api/test_log/<string:asset_number>', methods=['GET'])
def get_test_log_route(asset_number):
    if not asset_number or not asset_number.strip():
        return jsonify({'error': 'Asset number is required'}), 400

    logs = asset_test_logs.get(asset_number, [])
    since_index_str = request.args.get('since_index')

    if since_index_str is not None:
        try:
            since_index = int(since_index_str)
            # Return only new logs. Add 1 because client has logs *up to* since_index.
            logs_to_return = logs[since_index + 1:]
        except ValueError:
            return jsonify({'error': 'Invalid since_index, must be an integer.'}), 400
    else:
        logs_to_return = logs

    return jsonify({'logs': logs_to_return, 'last_index': len(logs) -1 if logs else -1})

@app.route('/api/available_tests', methods=['GET'])
def available_tests_route():
    # This list is based on the test_function_map in TestOrchestrator
    # Ideally, this would be dynamically generated or come from a shared configuration
    # For now, we'll hardcode it based on the known tests in TestOrchestrator.
    # We should only include tests that can be run headlessly or are platform-agnostic GUI tests
    # if the GUI tests are designed to be non-blocking or provide a result without user interaction
    # or if the profile editor simply needs a list of all *possible* tests.
    # The TestOrchestrator itself has logic to skip GUI tests in headless mode.
    # For profile editing purposes, it's probably best to list all known test paths.

    # Extracted from TestOrchestrator.test_function_map (keys)
    # Note: Some tests might be platform-specific (e.g., run_secure_wipe_test for Linux)
    # or might have visual components. The profile editor should list them, and the
    # orchestrator will handle skipping if necessary at runtime.
    known_test_paths = [
        'agent.tests.cpu_test.run_basic_cpu_test',
        'agent.tests.cpu_test.run_cpu_stress_test', # This one is conditionally added in orchestrator
        'agent.tests.ram_test.run_ram_test',
        'agent.tests.ram_test.run_advanced_ram_test',
        'agent.tests.display_test.run_lcd_test_gui',
        'agent.tests.keyboard_test.run_keyboard_test',
        'agent.tests.pointing_device_test.run_pointing_device_test',
        'agent.tests.visual_cpu_test.visual_cpu_test', # This is a visual alternative
        'agent.tests.visual_ram_test.run_visual_ram_test', # Visual alternative
        'agent.tests.drive_wipe_test.run_secure_wipe_test', # Linux specific in orchestrator
        'agent.tests.touch_screen_test.run_touch_screen_test',
        # Deprecated battery tests are handled differently by orchestrator (auto-skipped with note)
        # For selection, we might not want to list them, or list them with a (deprecated) tag.
        # For now, excluding them as orchestrator auto-skips.
        # 'agent.tests.battery_test.run_battery_test',
        # 'agent.tests.battery_test.run_battery_discharge_test',
        # 'agent.tests.battery_test.run_battery_charge_test',
        # 'agent.tests.battery_test.run_battery_full_assessment',
    ]

    # A more robust way would be to inspect TestOrchestrator's map if possible,
    # or have a shared list. For now, this synchronized list is okay.
    # We should also consider if run_cpu_stress_test is always available or depends on import.
    # The orchestrator map handles this: `**({'agent.tests.cpu_test.run_cpu_stress_test': ...} if run_cpu_stress_test else {})`
    # So, we might need to check for `run_cpu_stress_test`'s availability here too.

    # Simplified list for now, can be refined if direct inspection of TestOrchestrator is too complex here.
    # This list should represent the tests a user can choose to put into a profile.
    # The user-friendly names are derived by the frontend (e.g. in populateTestCheckboxes).
    # The profile stores these full paths.

    # Let's refine this by trying to import the test functions to see if they are available
    # This is closer to what TestOrchestrator does.

    potential_tests = {
        'Basic CPU Test': 'agent.tests.cpu_test.run_basic_cpu_test',
        'CPU Stress Test': 'agent.tests.cpu_test.run_cpu_stress_test',
        'Basic RAM Test': 'agent.tests.ram_test.run_ram_test',
        'Advanced RAM Test': 'agent.tests.ram_test.run_advanced_ram_test',
        'Display Test (LCD)': 'agent.tests.display_test.run_lcd_test_gui',
        'Keyboard Test': 'agent.tests.keyboard_test.run_keyboard_test',
        'Pointing Device Test': 'agent.tests.pointing_device_test.run_pointing_device_test',
        'Visual CPU Test': 'agent.tests.visual_cpu_test.visual_cpu_test',
        'Visual RAM Test': 'agent.tests.visual_ram_test.run_visual_ram_test',
        'Secure Wipe Test (Linux)': 'agent.tests.drive_wipe_test.run_secure_wipe_test',
        'Touch Screen Test': 'agent.tests.touch_screen_test.run_touch_screen_test',
    }

    available_display_tests = []
    for display_name, path in potential_tests.items():
        module_path, func_name = path.rsplit('.', 1)
        try:
            module = __import__(module_path, fromlist=[func_name])
            if hasattr(module, func_name):
                # For platform specific tests like wipe, we might add more checks or rely on orchestrator to skip
                # For now, if it can be imported, list it.
                available_display_tests.append({"name": display_name, "path": path})
            else:
                server_logger.warning(f"Could not find function {func_name} in {module_path} for available tests.")
        except ImportError:
            server_logger.warning(f"Could not import module {module_path} for available tests (path: {path}). Test may be platform specific or optional.")
        except Exception as e:
            server_logger.error(f"Unexpected error checking test {path}: {e}", exc_info=True)

    # Sort by display name for user convenience
    available_display_tests.sort(key=lambda x: x["name"])

    return jsonify(available_display_tests)


@app.route('/api/visual_test/start', methods=['POST'])
def start_visual_test_route():
    """Start a visual test session and return session ID."""
    if not request.json:
        return jsonify({'error': 'Invalid input, JSON required'}), 400

    data = request.json
    test_type = data.get('test_type')  # 'ram' or 'cpu'
    asset_number = data.get('asset_number')
    test_params = data.get('test_params', {})

    if not all([test_type, asset_number]):
        return jsonify({'error': 'Missing required fields: test_type, asset_number'}), 400

    if test_type not in ['ram', 'cpu', 'lcd']:
        return jsonify({'error': 'Invalid test_type. Must be "ram", "cpu", or "lcd"'}), 400

    # Generate unique session ID
    session_id = f"{test_type}_{asset_number}_{int(time.time())}"

    # Initialize session data
    with visual_test_lock:
        visual_test_sessions[session_id] = {
            'test_type': test_type,
            'asset_number': asset_number,
            'status': 'initializing',
            'progress': {
                'overall_progress': 0,
                'operation_progress': 0,
                'operation_text': 'Initializing...',
                'cycles': 0,
                'patterns': 0,
                'errors': 0,
                'speed': 0,
                'time_left': 0,
                'system_info': {},
                # LCD-specific fields
                'current_color': '',
                'current_color_hex': '',
                'color_index': 0,
                'total_colors': 0,
                'failed_colors': [],
                'awaiting_user_input': False
            },
            'test_params': test_params,
            'start_time': time.time(),
            'test_complete': False,
            'test_result': None
        }

    # Start the test in a background thread
    test_thread = threading.Thread(
        target=_run_visual_test_background,
        args=(session_id, test_type, test_params),
        daemon=True
    )
    test_thread.start()

    return jsonify({'session_id': session_id, 'message': 'Visual test started'}), 200


@app.route('/api/visual_test/progress/<session_id>')
def visual_test_progress_stream(session_id):
    """Server-Sent Events endpoint for streaming test progress."""
    def generate_progress():
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions.get(session_id)
                if not session_data:
                    yield f"data: {json.dumps({'error': 'Session not found'})}\n\n"
                    break

                # Send current progress data
                progress_data = {
                    'status': session_data['status'],
                    'progress': session_data['progress'],
                    'test_complete': session_data['test_complete']
                }

                if session_data['test_result']:
                    progress_data['test_result'] = session_data['test_result']

                yield f"data: {json.dumps(progress_data)}\n\n"

                # If test is complete, send final update and break
                if session_data['test_complete']:
                    break

            time.sleep(0.5)  # Update every 500ms

    return Response(
        generate_progress(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*'
        }
    )


@app.route('/api/visual_test/stop/<session_id>', methods=['POST'])
def stop_visual_test_route(session_id):
    """Stop a running visual test."""
    with visual_test_lock:
        session_data = visual_test_sessions.get(session_id)
        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        # Mark session for stopping
        session_data['status'] = 'stopping'
        session_data['stop_requested'] = True

    return jsonify({'message': 'Stop request sent'}), 200


def _run_visual_test_background(session_id, test_type, test_params):
    """Run visual test in background thread."""
    try:
        if test_type == 'ram':
            _run_ram_test_background(session_id, test_params)
        elif test_type == 'cpu':
            _run_cpu_test_background(session_id, test_params)
        elif test_type == 'lcd':
            _run_lcd_test_background(session_id, test_params)
    except Exception as e:
        server_logger.error(f"Error in visual test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            if session_id in visual_test_sessions:
                visual_test_sessions[session_id]['status'] = 'error'
                visual_test_sessions[session_id]['test_complete'] = True
                visual_test_sessions[session_id]['test_result'] = {
                    'status': 'ERROR',
                    'notes': f'Test error: {str(e)}'
                }


def _run_ram_test_background(session_id, test_params):
    """Run RAM test logic adapted for web UI."""
    import psutil

    # Get test parameters
    test_size_mb = test_params.get('test_size_mb', 1024)
    duration_seconds = test_params.get('duration_seconds', 30)

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'

    try:
        # Get system memory information
        mem_info = psutil.virtual_memory()
        total_mb = mem_info.total / (1024 * 1024)
        available_mb = mem_info.available / (1024 * 1024)

        # Update system info
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress']['system_info'] = {
                'total_mb': int(total_mb),
                'available_mb': int(available_mb)
            }

        # Determine actual test size
        test_size_mb = min(test_size_mb, int(available_mb * 0.25))
        test_size_bytes = test_size_mb * 1024 * 1024

        # Test patterns
        patterns = [b'\xAA', b'\x55', b'\xFF', b'\x00']
        pattern_names = [f"0x{p[0]:02X}" for p in patterns]

        # Update patterns count
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress']['patterns'] = len(patterns)

        # Track statistics
        cycles_completed = 0
        total_errors = 0
        start_time = time.time()
        end_time = start_time + duration_seconds

        # Main test loop
        while time.time() < end_time:
            # Check if stop was requested
            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                if session_data.get('stop_requested', False):
                    break

            cycles_completed += 1

            # Update progress
            elapsed = time.time() - start_time
            overall_progress = min(100, (elapsed / duration_seconds) * 100)
            time_left = max(0, duration_seconds - elapsed)

            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                session_data['progress'].update({
                    'overall_progress': overall_progress,
                    'cycles': cycles_completed,
                    'time_left': time_left,
                    'operation_text': 'Allocating memory...',
                    'operation_progress': 0
                })

            # Allocate memory
            try:
                buf = bytearray(test_size_bytes)
            except MemoryError:
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['status'] = 'error'
                    session_data['test_complete'] = True
                    session_data['test_result'] = {
                        'status': 'FAIL',
                        'notes': 'Memory allocation failed'
                    }
                return

            # Test each pattern
            for pattern_idx, pattern_byte in enumerate(patterns):
                if time.time() >= end_time:
                    break

                # Check if stop was requested
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    if session_data.get('stop_requested', False):
                        break

                pattern_hex = f"0x{pattern_byte[0]:02X}"

                # Write pattern
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['operation_text'] = f'Writing pattern {pattern_hex}...'

                start_write = time.time()
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break

                    chunk_size = min(4096, test_size_bytes - i)
                    buf[i:i+chunk_size] = pattern_byte * chunk_size

                    # Update operation progress
                    progress = (i / test_size_bytes) * 100
                    with visual_test_lock:
                        session_data = visual_test_sessions[session_id]
                        session_data['progress']['operation_progress'] = progress

                write_time = time.time() - start_write
                write_speed = (test_size_bytes / write_time) / (1024 * 1024) if write_time > 0 else 0

                # Update speed
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['speed'] = write_speed

                # Verify pattern
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['operation_text'] = f'Verifying pattern {pattern_hex}...'

                errors = 0
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break

                    chunk_size = min(4096, test_size_bytes - i)
                    chunk = buf[i:i+chunk_size]
                    expected = pattern_byte * chunk_size

                    if chunk != expected:
                        errors += 1

                    # Update operation progress
                    progress = (i / test_size_bytes) * 100
                    with visual_test_lock:
                        session_data = visual_test_sessions[session_id]
                        session_data['progress']['operation_progress'] = progress

                total_errors += errors

                # Update error count
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['errors'] = total_errors

        # Test complete
        total_time = time.time() - start_time
        status = 'PASS' if total_errors == 0 else 'FAIL'

        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'complete'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': status,
                'notes': f'Completed {cycles_completed} cycles with {total_errors} errors in {total_time:.1f} seconds',
                'cycles_completed': cycles_completed,
                'errors': total_errors,
                'duration_seconds': total_time,
                'test_size_mb': test_size_mb
            }

    except Exception as e:
        server_logger.error(f"Error in RAM test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'error'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': 'ERROR',
                'notes': f'Test error: {str(e)}'
            }


def _run_cpu_test_background(session_id, test_params):
    """Run CPU test logic adapted for web UI."""
    # This would be implemented similar to RAM test
    # For now, just a placeholder
    duration_seconds = test_params.get('duration_seconds', 10)

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress']['operation_text'] = 'Running CPU stress test...'

    # Simulate CPU test progress
    start_time = time.time()
    end_time = start_time + duration_seconds

    while time.time() < end_time:
        # Check if stop was requested
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            if session_data.get('stop_requested', False):
                break

        elapsed = time.time() - start_time
        progress = min(100, (elapsed / duration_seconds) * 100)
        time_left = max(0, duration_seconds - elapsed)

        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress'].update({
                'overall_progress': progress,
                'time_left': time_left
            })

        time.sleep(0.1)

    # Complete the test
    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['test_result'] = {
            'status': 'PASS',
            'notes': f'CPU test completed in {duration_seconds} seconds'
        }


def _run_lcd_test_background(session_id, test_params):
    """Run LCD test logic adapted for web UI."""
    # Test colors matching the original tkinter implementation
    test_colors = [
        ("Black", "#000000"),
        ("White", "#FFFFFF"),
        ("Red", "#FF0000"),
        ("Green", "#00FF00"),
        ("Blue", "#0000FF")
    ]

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress'].update({
            'total_colors': len(test_colors),
            'color_index': 0,
            'current_color': test_colors[0][0],
            'current_color_hex': test_colors[0][1],
            'operation_text': f'Displaying {test_colors[0][0]} - Press OK if correct, FAIL if not',
            'awaiting_user_input': True
        })

    try:
        # LCD test is user-driven, so we just wait for user input
        # The test progresses through user interaction via the input endpoint
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                if session_data.get('stop_requested', False) or session_data['test_complete']:
                    break

            time.sleep(0.1)  # Small sleep to prevent busy waiting

    except Exception as e:
        server_logger.error(f"Error in LCD test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'error'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': 'ERROR',
                'notes': f'Test error: {str(e)}'
            }


@app.route('/api/visual_test/input/<session_id>', methods=['POST'])
def visual_test_input_route(session_id):
    """Handle user input for visual tests (e.g., LCD test color approval)."""
    if not request.json:
        return jsonify({'error': 'Invalid input, JSON required'}), 400

    data = request.json
    action = data.get('action')  # 'ok', 'fail', 'next', etc.

    with visual_test_lock:
        session_data = visual_test_sessions.get(session_id)
        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        test_type = session_data['test_type']

        if test_type == 'lcd':
            return _handle_lcd_input(session_id, session_data, action)
        else:
            return jsonify({'error': f'Input not supported for test type: {test_type}'}), 400


def _handle_lcd_input(session_id, session_data, action):
    """Handle LCD test user input."""
    test_colors = [
        ("Black", "#000000"),
        ("White", "#FFFFFF"),
        ("Red", "#FF0000"),
        ("Green", "#00FF00"),
        ("Blue", "#0000FF")
    ]

    current_index = session_data['progress']['color_index']

    if action == 'fail':
        # Mark current color as failed
        current_color = test_colors[current_index][0]
        session_data['progress']['failed_colors'].append(current_color)
        session_data['progress']['errors'] += 1
        server_logger.info(f"LCD test {session_id}: Color {current_color} marked as FAILED")
    elif action == 'ok':
        # Current color is OK
        current_color = test_colors[current_index][0]
        server_logger.info(f"LCD test {session_id}: Color {current_color} marked as OK")
    else:
        return jsonify({'error': 'Invalid action. Must be "ok" or "fail"'}), 400

    # Move to next color
    next_index = current_index + 1

    if next_index < len(test_colors):
        # Update to next color
        session_data['progress'].update({
            'color_index': next_index,
            'current_color': test_colors[next_index][0],
            'current_color_hex': test_colors[next_index][1],
            'operation_text': f'Displaying {test_colors[next_index][0]} - Press OK if correct, FAIL if not',
            'overall_progress': (next_index / len(test_colors)) * 100,
            'awaiting_user_input': True
        })
    else:
        # Test complete
        failed_colors = session_data['progress']['failed_colors']
        status = 'PASS' if len(failed_colors) == 0 else 'FAIL'

        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['progress']['awaiting_user_input'] = False
        session_data['progress']['overall_progress'] = 100
        session_data['test_result'] = {
            'status': status,
            'notes': f'LCD test completed. Failed colors: {", ".join(failed_colors)}' if failed_colors else 'All colors displayed correctly',
            'failed_colors': failed_colors,
            'total_colors': len(test_colors)
        }

        server_logger.info(f"LCD test {session_id} completed: {status}")

    return jsonify({'message': 'Input processed', 'test_complete': session_data['test_complete']}), 200


if __name__ == '__main__':
    # Ensure logging is set up for direct execution
    if not server_logger.hasHandlers():
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.DEBUG) # Or INFO for less verbosity
    # app.run(debug=True, host='0.0.0.0', port=5000) # 실행은 run_web_ui.py에서 담당
    server_logger.info("web_server/app.py executed directly. Server will not start from here. Run run_web_ui.py to start.")


@app.route('/api/system_info')
def system_info_route():
    try:
        info = get_system_info()
        return jsonify(info)
    except Exception as e:
        print(f'Error in /api/system_info: {e!s}')
        return (jsonify({'error': 'Failed to retrieve system information', 'details': str(e)}), 500)

def profile_exists(profile_name):
    return os.path.exists(get_profile_path(profile_name))

@app.route('/api/profiles', methods=['GET'])
def list_profiles_route():
    profiles = get_all_profiles()
    return jsonify([p.to_dict() for p in profiles])

@app.route('/api/profiles/<string:profile_name>', methods=['GET'])
def get_profile_route(profile_name):
    profile = load_profile(profile_name)
    if profile:
        return jsonify(profile.to_dict())
    else:
        return (jsonify({'error': 'Profile not found'}), 404)

@app.route('/api/profiles', methods=['POST'])
def create_profile_route():
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    data = request.json
    required_fields = ['name', 'tests']
    for field in required_fields:
        if field not in data:
            return (jsonify({'error': f'Missing field: {field}'}), 400)
    profile_name = data.get('name')
    if not profile_name or not profile_name.strip():
        return (jsonify({'error': 'Profile name cannot be empty'}), 400)
    if profile_exists(profile_name):
        return (jsonify({'error': f"Profile '{profile_name}' already exists"}), 409)
    new_profile = Profile(name=profile_name, description=data.get('description', ''), tests=data.get('tests', []), device_type=data.get('device_type', 'Generic'), test_args=data.get('test_args', {}))
    if save_profile(new_profile):
        return (jsonify(new_profile.to_dict()), 201)
    else:
        return (jsonify({'error': 'Failed to save profile'}), 500)

@app.route('/api/profiles/<string:profile_name>', methods=['PUT'])
def update_profile_route(profile_name):
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    if not profile_exists(profile_name):
        return (jsonify({'error': 'Profile not found'}), 404)
    data = request.json
    if 'name' in data and data['name'] != profile_name:
        return (jsonify({'error': f"Cannot change profile name from '{profile_name}' to '{data['name']}'. Delete and recreate if necessary."}), 400)
    updated_profile = Profile(name=profile_name, description=data.get('description', load_profile(profile_name).description if load_profile(profile_name) else ''), tests=data.get('tests', load_profile(profile_name).tests if load_profile(profile_name) else []), device_type=data.get('device_type', load_profile(profile_name).device_type if load_profile(profile_name) else 'Generic'), test_args=data.get('test_args', load_profile(profile_name).test_args if load_profile(profile_name) else {}))
    if save_profile(updated_profile):
        return (jsonify(updated_profile.to_dict()), 200)
    else:
        return (jsonify({'error': 'Failed to update profile'}), 500)

@app.route('/api/profiles/<string:profile_name>', methods=['DELETE'])
def delete_profile_route(profile_name):
    if not profile_exists(profile_name):
        return (jsonify({'error': 'Profile not found'}), 404)
    if delete_profile(profile_name):
        return (jsonify({}), 204)
    else:
        return (jsonify({'error': 'Failed to delete profile'}), 500)

@app.route('/api/results/<string:asset_number>', methods=['GET'])
def list_asset_results_route(asset_number):
    if not os.path.isdir(RESULTS_DIR):
        return (jsonify({'error': 'Results directory not found on server'}), 404)
    consolidated_pattern = os.path.join(RESULTS_DIR, f'consolidated_nexus_results_{asset_number}.json')
    consolidated_files = [os.path.basename(f) for f in glob.glob(consolidated_pattern)]
    individual_pattern = os.path.join(RESULTS_DIR, f'nexus_result_{asset_number}_*.json')
    individual_files = [os.path.basename(f) for f in glob.glob(individual_pattern)]
    if not consolidated_files and (not individual_files):
        return (jsonify({'error': 'No results found for this asset number'}), 404)
    return jsonify({'asset_number': asset_number, 'consolidated_results': consolidated_files, 'individual_results': individual_files})

@app.route('/api/results/<string:asset_number>/<string:result_filename>', methods=['GET'])
def get_specific_result_route(asset_number, result_filename):
    if '..' in result_filename or result_filename.startswith('/'):
        return (jsonify({'error': 'Invalid filename'}), 400)
    file_path = os.path.join(RESULTS_DIR, result_filename)
    expected_consolidated_prefix = f'consolidated_nexus_results_{asset_number}.json'
    expected_individual_prefix = f'nexus_result_{asset_number}_'
    is_valid_consolidated = result_filename == expected_consolidated_prefix
    is_valid_individual = result_filename.startswith(expected_individual_prefix) and result_filename.endswith('.json')
    if not (is_valid_consolidated or is_valid_individual):
        return (jsonify({'error': 'Filename does not match asset number pattern or expected format'}), 400)
    abs_results_dir = os.path.abspath(RESULTS_DIR)
    abs_file_path = os.path.join(abs_results_dir, result_filename)
    if os.path.exists(abs_file_path) and os.path.isfile(abs_file_path):
        return send_from_directory(abs_results_dir, result_filename, as_attachment=False)
    else:
        return (jsonify({'error': 'Result file not found'}), 404)

@app.route('/api/device_conditions/<string:asset_number>', methods=['GET'])
def get_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return (jsonify({'error': 'Asset number is required'}), 400)
    conditions = load_device_conditions(asset_number)
    if conditions:
        return (jsonify(conditions), 200)
    else:
        return (jsonify({'message': 'No conditions found for this asset, or error loading.', 'default_structure_if_needed': DEFAULT_DEVICE_CONDITIONS_STRUCTURE}), 404)

@app.route('/api/device_conditions/<string:asset_number>', methods=['POST'])
def save_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return (jsonify({'error': 'Asset number is required'}), 400)
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    conditions_data = request.json
    if save_device_conditions(conditions_data, asset_number):
        return (jsonify({'message': 'Device conditions saved successfully'}), 200)
    else:
        return (jsonify({'error': 'Failed to save device conditions'}), 500)
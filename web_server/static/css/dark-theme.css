/* Dark Theme for Nexus Agent */

body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1f1f1f;
    color: #ffffff;
    padding: 1rem;
    border-bottom: 1px solid #333;
}

/* Asset Header Styling - Compact */
.asset-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 0.5rem;
    padding: 0.75rem;
    background-color: #2a2a2a;
    border-radius: 6px;
    border: 1px solid #5565f7;
}

.asset-input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.asset-input-group label {
    font-size: 0.8rem;
    font-weight: bold;
    color: #5565f7;
    margin-bottom: 0;
}

.asset-input-group input {
    width: 140px;
    padding: 8px;
    font-size: 0.95rem;
    text-align: center;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.asset-input-group input:focus {
    border-color: #5565f7;
    outline: none;
}

.asset-status {
    display: flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    background-color: #333;
    min-width: 160px;
    justify-content: center;
    font-size: 0.9rem;
}

.asset-status.ready {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.asset-status.incomplete {
    background-color: #4d1e1e;
    color: #f44336;
}

main {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.secondary-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.dashboard-card {
    background-color: #1e1e1e;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #333;
    transition: border-color 0.3s ease;
}

.dashboard-card:hover {
    border-color: #555;
}

.priority-card {
    border-color: #5565f7;
    background-color: #1e1e2e;
}

.full-width {
    grid-column: 1 / -1;
}

/* Collapsible Sections */
.collapsible .collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible.expanded .collapsible-content {
    max-height: 500px;
}

.collapsible-header {
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.collapsible.expanded .collapse-icon {
    transform: rotate(180deg);
}

h2 {
    color: #5565f7;
    border-bottom: 2px solid #5565f7;
    padding-bottom: 0.5rem;
    margin-top: 0;
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #e0e0e0;
}

/* Input Styling */
input[type='text'],
textarea,
select {
    width: calc(100% - 20px);
    padding: 10px;
    margin-bottom: 1rem;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    font-size: 1rem;
}

input[type='text']:focus,
textarea:focus,
select:focus {
    border-color: #5565f7;
    outline: none;
}

/* Button Styling */
button {
    background-color: #3256b0;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-height: 44px; /* Touch-friendly */
}

button:hover {
    background-color: #7c94f7;
    transform: translateY(-1px);
}

button:disabled {
    background-color: #555;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: #5565f7;
    font-weight: bold;
}

.btn-primary:hover {
    background-color: #7c94f7;
}

.btn-secondary {
    background-color: #666;
}

.btn-secondary:hover {
    background-color: #888;
}

.btn-large {
    padding: 15px 25px;
    font-size: 1.2rem;
    min-height: 50px;
}

.btn-compact {
    padding: 8px 12px;
    font-size: 0.9rem;
    min-height: 36px;
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Test Profile Section Styling */
.test-profile-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.profile-selection {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.profile-selection label {
    margin-bottom: 0;
    white-space: nowrap;
}

.profile-select {
    flex: 1;
    min-width: 200px;
    padding: 10px;
    font-size: 1rem;
}

.profile-details-preview {
    background-color: #2a2a2a;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    max-height: 120px;
    overflow-y: auto;
    border-left: 3px solid #5565f7;
}

.profile-details-preview:empty {
    display: none;
}

.profile-details-preview h4 {
    margin: 0 0 0.5rem 0;
    color: #5565f7;
    font-size: 1rem;
}

.profile-details-preview .profile-info {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.5rem 1rem;
    align-items: start;
}

.profile-details-preview .profile-info strong {
    color: #e0e0e0;
}

.test-execution-controls {
    display: flex;
    justify-content: center;
    padding-top: 0.5rem;
}

/* Visual Tests Section */
.visual-tests-content {
    text-align: center;
}

.visual-tests-description {
    font-size: 0.9rem;
    color: #aaa;
    margin-bottom: 1rem;
}

.visual-test-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Compact Info Display */
.compact-info {
    font-size: 0.9rem;
    line-height: 1.4;
}

.compact-info ul {
    margin: 0;
    padding-left: 1rem;
}

.compact-info li {
    margin-bottom: 0.3rem;
}

/* Device Conditions Compact */
.device-conditions-compact {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    background-color: #333;
}

.status-indicator.success {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.status-indicator.warning {
    background-color: #4d3d1e;
    color: #ff9800;
}

/* Profile Management Compact */
.profile-quick-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.profile-select-main {
    flex: 1;
    min-width: 200px;
}

.profile-details-compact {
    background-color: #2a2a2a;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
}

/* Results Viewer Compact */
.results-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.results-alt-input {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.results-alt-input input {
    flex: 1;
    margin-bottom: 0;
}

.results-list-compact {
    max-height: 200px;
    overflow-y: auto;
    background-color: #2a2a2a;
    padding: 1rem;
    border-radius: 4px;
}

/* Test Log Styling */
.test-log {
    background-color: #1a1a1a;
    padding: 1rem;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    border: 1px solid #333;
}

/* Modal Styling */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #2c2c2c;
    padding: 20px;
    border: 1px solid #888;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-large {
    max-width: 800px;
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close-button:hover,
.close-button:focus {
    color: white;
    text-decoration: none;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #444;
}

/* Form Styling */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    max-height: 150px;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: #333;
    border-radius: 4px;
}

.checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-grid input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Device Conditions Form */
.device-conditions-form {
    max-height: 400px;
    overflow-y: auto;
}

.dc-category {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #333;
    border-radius: 4px;
}

.dc-category h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #5565f7;
    border-bottom: 1px solid #5565f7;
    padding-bottom: 0.5rem;
}

.dc-item {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

.dc-item label {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.dc-item input {
    margin-bottom: 0;
}

/* Status Messages */
.status-message {
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 1rem;
    text-align: center;
}

.status-message.success {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.status-message.error {
    background-color: #4d1e1e;
    color: #f44336;
}

.status-message.info {
    background-color: #1e3a4d;
    color: #2196F3;
}

/* Result Details */
.result-details-content {
    max-height: 500px;
    overflow-y: auto;
    background-color: #1a1a1a;
    padding: 1rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

footer {
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
    background-color: #1f1f1f;
    color: #aaa;
    border-top: 1px solid #333;
}

/* System Info Section Styling */
.system-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.system-info-section {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.system-info-section h3 {
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid;
}

.system-info-cpu {
    background-color: #1e2b3a;
    border-left: 4px solid #4a90e2;
}

.system-info-cpu h3 {
    color: #4a90e2;
    border-bottom-color: #4a90e2;
}

.system-info-ram {
    background-color: #1e3a2a;
    border-left: 4px solid #4ae287;
}

.system-info-ram h3 {
    color: #4ae287;
    border-bottom-color: #4ae287;
}

.system-info-disks {
    background-color: #3a1e2b;
    border-left: 4px solid #e24a90;
}

.system-info-disks h3 {
    color: #e24a90;
    border-bottom-color: #e24a90;
}

.system-info-gpus {
    background-color: #2b1e3a;
    border-left: 4px solid #904ae2;
}

.system-info-gpus h3 {
    color: #904ae2;
    border-bottom-color: #904ae2;
}

.system-info-other {
    background-color: #2b3a1e;
    border-left: 4px solid #90e24a;
}

.system-info-other h3 {
    color: #90e24a;
    border-bottom-color: #90e24a;
}

.system-info-item {
    margin: 0.5rem 0;
}

.system-info-item strong {
    color: #ddd;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }

    .asset-header {
        gap: 0.75rem;
    }

    .profile-selection {
        flex-direction: column;
        align-items: stretch;
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }

    .asset-header {
        padding: 0.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .asset-input-group input {
        width: 120px;
    }

    .profile-selection {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .profile-select {
        min-width: auto;
    }

    .profile-quick-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .profile-select-main {
        min-width: auto;
    }

    .results-alt-input {
        flex-direction: column;
        align-items: stretch;
    }

    .visual-test-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        width: 95%;
        padding: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .dc-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    main {
        padding: 0.5rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .asset-header {
        margin-top: 0.5rem;
        padding: 0.5rem;
    }

    .btn-large {
        padding: 12px 20px;
        font-size: 1.1rem;
    }

    h2 {
        font-size: 1.2rem;
    }
}

/* Dark Theme for Nexus Agent */

body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1f1f1f;
    color: #ffffff;
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid #333;
}

main {
    padding: 1rem;
}

.container {
    display: flex;
    gap: 1rem;
}

.column {
    flex: 1;
    background-color: #1e1e1e;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #333;
}

section {
    background-color: #1e1e1e;
    margin-bottom: 1rem;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #333;
}

h2 {
    color: #5565f7;
    border-bottom: 2px solid #5565f7;
    padding-bottom: 0.5rem;
    margin-top: 0;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

input[type='text'],
textarea,
select {
    width: calc(100% - 20px);
    padding: 10px;
    margin-bottom: 1rem;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
}

button {
    background-color: #3256b0;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #7c94f7;
}

button:disabled {
    background-color: #555;
    cursor: not-allowed;
}

.modal {
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
    background-color: #2c2c2c;
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close-button:hover,
.close-button:focus {
    color: white;
    text-decoration: none;
    cursor: pointer;
}

footer {
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
    background-color: #1f1f1f;
    color: #aaa;
    border-top: 1px solid #333;
}

/* System Info Section Styling */
.system-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.system-info-section {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.system-info-section h3 {
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid;
}

.system-info-cpu {
    background-color: #1e2b3a;
    border-left: 4px solid #4a90e2;
}

.system-info-cpu h3 {
    color: #4a90e2;
    border-bottom-color: #4a90e2;
}

.system-info-ram {
    background-color: #1e3a2a;
    border-left: 4px solid #4ae287;
}

.system-info-ram h3 {
    color: #4ae287;
    border-bottom-color: #4ae287;
}

.system-info-disks {
    background-color: #3a1e2b;
    border-left: 4px solid #e24a90;
}

.system-info-disks h3 {
    color: #e24a90;
    border-bottom-color: #e24a90;
}

.system-info-gpus {
    background-color: #2b1e3a;
    border-left: 4px solid #904ae2;
}

.system-info-gpus h3 {
    color: #904ae2;
    border-bottom-color: #904ae2;
}

.system-info-other {
    background-color: #2b3a1e;
    border-left: 4px solid #90e24a;
}

.system-info-other h3 {
    color: #90e24a;
    border-bottom-color: #90e24a;
}

.system-info-item {
    margin: 0.5rem 0;
}

.system-info-item strong {
    color: #ddd;
}

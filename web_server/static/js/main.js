console.log("Nexus Agent UI Loaded");

async function fetchData(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error("Could not fetch data from " + url + ":", error);
        return null; // Return null or an empty object to handle errors gracefully
    }
}

function formatSystemInfo(info) {
    if (!info) {
        return "<p>Could not load system information.</p>";
    }

    let html = '<ul>';
    html += `<li><strong>Serial #:</strong> ${info.serial_number || 'N/A'}</li>`;
    html += `<li><strong>CPU:</strong> ${info.cpu || 'N/A'}</li>`;
    html += `<li><strong>Memory:</strong> ${info.memory || 'N/A'}</li>`;
    html += `<li><strong>Graphics:</strong> ${(info.gpus && info.gpus.length > 0) ? info.gpus.join(', ') : 'N/A'}</li>`;
    html += `<li><strong>Screen Resolution:</strong> ${info.screen_resolution || 'N/A'}</li>`;

    if (info.battery && info.battery.present) {
        html += `<li><strong>Battery:</strong> ${info.battery.percent || 'N/A'}% (${info.battery.status || 'N/A'})`;
        if (info.battery.health) html += `, Health: ${info.battery.health}`;
        if (info.battery.cycle_count) html += `, Cycles: ${info.battery.cycle_count}`;
        html += `</li>`;
    } else {
        html += `<li><strong>Battery:</strong> No battery detected</li>`;
    }

    html += '<li><strong>Disks:</strong></li>';
    if (info.disks && info.disks.length > 0) {
        html += '<ul>';
        info.disks.forEach((disk, index) => {
            html += `<li>Drive ${index}: ${disk.model || 'Unknown'} - ${disk.size_gb || '?'}GB (${disk.type || 'Unknown'}) - SMART: ${disk.smart_passed === null ? 'Unknown' : (disk.smart_passed ? 'OK' : 'BAD')}</li>`;
        });
        html += '</ul>';
    } else {
        html += '<ul><li>No disks detected</li></ul>';
    }
    html += '</ul>';
    return html;
}

async function loadSystemInfo() {
    const systemInfoContent = document.getElementById('system-info-content');
    if (!systemInfoContent) {
        console.error("System info content area not found.");
        return;
    }
    const data = await fetchData('/api/system_info');
    systemInfoContent.innerHTML = formatSystemInfo(data);
}

// Global variables to store asset information
let operatorId = '';
let assetNumber = '';

function updateAssetInfo() {
    operatorId = document.getElementById('operator-id').value;
    assetNumber = document.getElementById('asset-number').value;
    console.log(`Operator ID: ${operatorId}, Asset Number: ${assetNumber}`);
}


document.addEventListener('DOMContentLoaded', () => {
    loadSystemInfo();
    initializeUI();

    const operatorIdInput = document.getElementById('operator-id');
    const assetNumberInput = document.getElementById('asset-number');

    if (operatorIdInput) {
        operatorIdInput.addEventListener('input', updateAssetInfo);
        operatorIdInput.addEventListener('keypress', handleEnterKey);
    }
    if (assetNumberInput) {
        assetNumberInput.addEventListener('input', updateAssetInfo);
        assetNumberInput.addEventListener('keypress', handleEnterKey);
    }

    // Profile Management - Both selects
    loadProfiles();
    setupProfileModal();
    setupQuickActions();

    const loadProfileDetailsBtn = document.getElementById('load-profile-details-btn');
    if (loadProfileDetailsBtn) {
        loadProfileDetailsBtn.addEventListener('click', displaySelectedProfileDetails);
    }

    const createProfileBtn = document.getElementById('create-profile-btn');
    if (createProfileBtn) {
        createProfileBtn.addEventListener('click', openProfileModalForCreate);
    }

    const editProfileBtn = document.getElementById('edit-profile-btn');
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', openProfileModalForEdit);
    }

    const deleteProfileBtn = document.getElementById('delete-profile-btn');
    if (deleteProfileBtn) {
        deleteProfileBtn.addEventListener('click', deleteSelectedProfile);
    }

    // Profile select handlers for both dropdowns
    const profileSelect = document.getElementById('profile-select');
    const profileSelectQuick = document.getElementById('profile-select-quick');

    if (profileSelect) {
        profileSelect.addEventListener('change', () => {
            const selectedProfileName = profileSelect.value;
            const isProfileSelected = selectedProfileName && selectedProfileName !== "";
            if(editProfileBtn) editProfileBtn.disabled = !isProfileSelected;
            if(deleteProfileBtn) deleteProfileBtn.disabled = !isProfileSelected;
            if (!isProfileSelected) {
                 document.getElementById('profile-details').innerHTML = '';
            }
            // Sync with quick select
            if (profileSelectQuick) profileSelectQuick.value = selectedProfileName;
            checkRunTestButtonState();
        });
    }

    if (profileSelectQuick) {
        profileSelectQuick.addEventListener('change', () => {
            const selectedProfileName = profileSelectQuick.value;
            // Sync with main select
            if (profileSelect) profileSelect.value = selectedProfileName;
            checkRunTestButtonState();
        });
    }

    // Test Execution - Both buttons
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');

    if (runTestsBtn) {
        runTestsBtn.addEventListener('click', runTests);
    }
    if (runTestsBtnQuick) {
        runTestsBtnQuick.addEventListener('click', runTests);
    }

    // Initial check for run tests button state
    checkRunTestButtonState();

    // Hook asset/operator input changes to check button state
    if(operatorIdInput) operatorIdInput.addEventListener('input', checkRunTestButtonState);
    if(assetNumberInput) assetNumberInput.addEventListener('input', checkRunTestButtonState);

    // Device Conditions
    setupDeviceConditions();

    // Results Viewer
    setupResultsViewer();
});

// --- UI Initialization Functions ---
function initializeUI() {
    // Initialize collapsible sections as collapsed
    const collapsibleSections = document.querySelectorAll('.collapsible');
    collapsibleSections.forEach(section => {
        section.classList.remove('expanded');
    });

    // Update asset status
    updateAssetStatus();
}

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        // Focus next input or trigger action
        const operatorId = document.getElementById('operator-id').value;
        const assetNumber = document.getElementById('asset-number').value;

        if (event.target.id === 'operator-id' && operatorId) {
            document.getElementById('asset-number').focus();
        } else if (event.target.id === 'asset-number' && assetNumber && operatorId) {
            // Focus profile select if both fields are filled
            const profileSelect = document.getElementById('profile-select-quick');
            if (profileSelect) profileSelect.focus();
        }
    }
}

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.toggle('expanded');
    }
}

function setupQuickActions() {
    // Quick results button
    const listResultsBtnQuick = document.getElementById('list-results-btn-quick');
    if (listResultsBtnQuick) {
        listResultsBtnQuick.addEventListener('click', () => {
            const assetNumber = document.getElementById('asset-number').value;
            if (assetNumber) {
                document.getElementById('results-asset-number').value = assetNumber;
                listResultsForAsset();
            } else {
                alert('Please enter an asset number first.');
            }
        });
    }
}

function setupDeviceConditions() {
    const loadDcBtnQuick = document.getElementById('load-dc-btn-quick');
    if (loadDcBtnQuick) {
        loadDcBtnQuick.addEventListener('click', openDeviceConditionsModal);
    }
}

function setupResultsViewer() {
    const listResultsBtn = document.getElementById('list-results-btn');
    if (listResultsBtn) {
        listResultsBtn.addEventListener('click', listResultsForAsset);
    }

    const resultsAssetNumberInput = document.getElementById('results-asset-number');
    if (resultsAssetNumberInput) {
        resultsAssetNumberInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                listResultsForAsset();
            }
        });
    }
}

// --- Asset Info Update (modified to include run test button check) ---
function updateAssetInfo() {
    operatorId = document.getElementById('operator-id').value;
    assetNumber = document.getElementById('asset-number').value;
    console.log(`Operator ID: ${operatorId}, Asset Number: ${assetNumber}`);
    updateAssetStatus();
    checkRunTestButtonState(); // Check button state whenever asset info changes
}

function updateAssetStatus() {
    const statusElement = document.getElementById('asset-status');
    const statusText = document.getElementById('asset-status-text');
    const operatorId = document.getElementById('operator-id').value;
    const assetNumber = document.getElementById('asset-number').value;

    if (statusElement && statusText) {
        if (operatorId && assetNumber) {
            statusElement.className = 'asset-status ready';
            statusText.textContent = `Ready: ${operatorId} / ${assetNumber}`;
        } else {
            statusElement.className = 'asset-status incomplete';
            statusText.textContent = 'Enter asset information to begin';
        }
    }
}


// --- Profile Management Functions ---
let availableTests = []; // To be populated, e.g. from a dedicated endpoint or config

async function loadProfiles() {
    const profileSelect = document.getElementById('profile-select');
    const profileSelectQuick = document.getElementById('profile-select-quick');

    const profiles = await fetchData('/api/profiles');
    if (!profiles) {
        if (profileSelect) profileSelect.innerHTML = '<option value="">Error loading profiles</option>';
        if (profileSelectQuick) profileSelectQuick.innerHTML = '<option value="">Error loading profiles</option>';
        return;
    }

    // Update both profile selects
    const optionHTML = '<option value="">-- Select Profile --</option>' +
        profiles.map(profile => `<option value="${profile.name}">${profile.name}</option>`).join('');

    if (profileSelect) {
        profileSelect.innerHTML = optionHTML;
    }
    if (profileSelectQuick) {
        profileSelectQuick.innerHTML = optionHTML;
    }

    // After loading, ensure buttons are in correct state
    const editBtn = document.getElementById('edit-profile-btn');
    const deleteBtn = document.getElementById('delete-profile-btn');
    if(editBtn) editBtn.disabled = true;
    if(deleteBtn) deleteBtn.disabled = true;

    const profileDetails = document.getElementById('profile-details');
    if (profileDetails) profileDetails.innerHTML = '';
}

async function displaySelectedProfileDetails() {
    const profileSelect = document.getElementById('profile-select');
    const profileDetailsDiv = document.getElementById('profile-details');
    if (!profileSelect || !profileDetailsDiv) return;

    const profileName = profileSelect.value;
    if (!profileName) {
        profileDetailsDiv.innerHTML = '<p>Please select a profile to see details.</p>';
        return;
    }

    profileDetailsDiv.innerHTML = '<p>Loading details...</p>';
    const profile = await fetchData(`/api/profiles/${profileName}`);
    if (!profile) {
        profileDetailsDiv.innerHTML = `<p>Error loading details for ${profileName}.</p>`;
        return;
    }

    let html = `<h3>${profile.name}</h3>`;
    html += `<p><strong>Description:</strong> ${profile.description || 'N/A'}</p>`;
    html += `<p><strong>Device Type:</strong> ${profile.device_type || 'N/A'}</p>`;
    html += `<p><strong>Tests:</strong></p><ul>`;
    if (profile.tests && profile.tests.length > 0) {
        profile.tests.forEach(test => {
            html += `<li>${test}</li>`;
        });
    } else {
        html += `<li>No tests configured.</li>`;
    }
    html += `</ul>`;
    if (profile.test_args && Object.keys(profile.test_args).length > 0) {
        html += `<p><strong>Test Arguments:</strong></p><pre>${JSON.stringify(profile.test_args, null, 2)}</pre>`;
    } else {
        html += `<p><strong>Test Arguments:</strong> None</p>`;
    }
    profileDetailsDiv.innerHTML = html;
}

function setupProfileModal() {
    const modal = document.getElementById('profile-modal');
    const closeButton = document.querySelector('.modal .close-button');
    const profileForm = document.getElementById('profile-form');

    if (closeButton) {
        closeButton.onclick = () => modal.style.display = "none";
    }
    window.onclick = (event) => {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    }
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileFormSubmit);
    }
    // Populate available tests by fetching from the new endpoint
    fetchAndPopulateAvailableTests();
}

async function fetchAndPopulateAvailableTests() {
    const testsData = await fetchData('/api/available_tests'); // Expects array of {name: "Display", path: "module.path"}
    if (testsData && Array.isArray(testsData)) {
        populateTestCheckboxes(testsData);
    } else {
        console.error("Could not fetch or parse available tests. Using fallback or empty list.");
        populateTestCheckboxes([]); // Fallback to an empty list
    }
}

function populateTestCheckboxes(testsWithOptions) { // Expects array of {name: "Display Name", path: "module.path"}
    // availableTests = testsWithOptions.map(t => t.path); // Storing full test objects might be more useful
    availableTests = testsWithOptions; // Store the full objects {name, path}
    const checkboxesContainer = document.getElementById('profile-tests-checkboxes');
    if (!checkboxesContainer) return;

    checkboxesContainer.innerHTML = ''; // Clear existing
    if (testsWithOptions.length === 0) {
        checkboxesContainer.innerHTML = '<p>No tests available or could not load test list.</p>';
        return;
    }

    testsWithOptions.forEach(testOption => {
        const label = document.createElement('label');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = 'profile-test';
        checkbox.value = testOption.path; // Value submitted will be the full path

        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(` ${testOption.name}`)); // Display user-friendly name
        checkboxesContainer.appendChild(label);
    });
}


async function openProfileModalForCreate() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');

    if (!modal || !modalTitle || !profileForm) return;

    modalTitle.textContent = 'Create Profile';
    profileForm.reset(); // Clear form
    document.getElementById('profile-id').value = ''; // Ensure no ID for creation
    document.getElementById('profile-name').disabled = false; // Enable name field for new profiles

    // Reset checkboxes
    const checkboxes = profileForm.querySelectorAll('input[name="profile-test"]');
    checkboxes.forEach(cb => cb.checked = false);

    modal.style.display = 'block';
}

async function openProfileModalForEdit() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');
    const profileSelect = document.getElementById('profile-select');

    if (!modal || !modalTitle || !profileForm || !profileSelect) return;

    const profileName = profileSelect.value;
    if (!profileName) {
        alert('Please select a profile to edit.');
        return;
    }

    const profile = await fetchData(`/api/profiles/${profileName}`);
    if (!profile) {
        alert(`Could not load profile: ${profileName}`);
        return;
    }

    modalTitle.textContent = 'Edit Profile';
    profileForm.reset();
    document.getElementById('profile-id').value = profile.name; // Use name as ID for PUT
    document.getElementById('profile-name').value = profile.name;
    document.getElementById('profile-name').disabled = true; // Typically, name is not changed on edit via PUT to same resource
    document.getElementById('profile-description').value = profile.description || '';
    document.getElementById('profile-device-type').value = profile.device_type || '';
    document.getElementById('profile-test-args').value = profile.test_args ? JSON.stringify(profile.test_args, null, 2) : '{}';

    const checkboxes = profileForm.querySelectorAll('input[name="profile-test"]');
    checkboxes.forEach(cb => {
        cb.checked = profile.tests && profile.tests.includes(cb.value);
    });

    modal.style.display = 'block';
}

async function handleProfileFormSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const profileId = document.getElementById('profile-id').value; // Name for existing, empty for new
    const profileName = document.getElementById('profile-name').value;

    const selectedTests = Array.from(form.querySelectorAll('input[name="profile-test"]:checked'))
                              .map(cb => cb.value);
    let testArgs = {};
    try {
        const testArgsRaw = document.getElementById('profile-test-args').value.trim();
        if (testArgsRaw) {
            testArgs = JSON.parse(testArgsRaw);
        }
    } catch (e) {
        alert('Test Arguments are not valid JSON. Please correct them.');
        return;
    }

    const profileData = {
        name: profileName,
        description: document.getElementById('profile-description').value,
        device_type: document.getElementById('profile-device-type').value,
        tests: selectedTests,
        test_args: testArgs
    };

    let response;
    if (profileId && profileId === profileName) { // Editing existing profile (name is the identifier)
        response = await fetch(`/api/profiles/${profileId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
    } else { // Creating new profile
         if (!profileName.trim()) {
            alert("Profile name cannot be empty.");
            return;
        }
        response = await fetch('/api/profiles', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
    }

    if (response.ok) {
        alert(`Profile ${profileId ? 'updated' : 'created'} successfully!`);
        document.getElementById('profile-modal').style.display = 'none';
        loadProfiles(); // Refresh list
        // If it was an edit, also refresh details if that profile is still selected
        const currentSelectedProfile = document.getElementById('profile-select').value;
        if (currentSelectedProfile === profileName) {
            displaySelectedProfileDetails();
        }
    } else {
        const errorData = await response.json();
        alert(`Error: ${errorData.error || 'Failed to save profile.'}`);
    }
}

async function deleteSelectedProfile() {
    const profileSelect = document.getElementById('profile-select');
    if (!profileSelect) return;
    const profileName = profileSelect.value;

    if (!profileName) {
        alert('Please select a profile to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete profile "${profileName}"?`)) {
        const response = await fetch(`/api/profiles/${profileName}`, { method: 'DELETE' });
        if (response.ok) {
            alert(`Profile "${profileName}" deleted successfully.`);
            loadProfiles(); // Refresh list
            document.getElementById('profile-details').innerHTML = ''; // Clear details
        } else {
            const errorData = await response.json();
            alert(`Error deleting profile: ${errorData.error || 'Failed to delete profile.'}`);
        }
    }
}

// --- Test Execution Functions ---
let logPollInterval = null;
let lastLogIndex = -1;
let currentPollingAssetNumber = null;

function stopLogPolling() {
    if (logPollInterval) {
        clearInterval(logPollInterval);
        logPollInterval = null;
        console.log("Stopped log polling.");
    }
}

async function pollLogs() {
    if (!currentPollingAssetNumber) {
        stopLogPolling();
        return;
    }
    const testStatusLog = document.getElementById('test-status-log');
    if (!testStatusLog) {
        stopLogPolling();
        return;
    }

    try {
        const response = await fetch(`/api/test_log/${currentPollingAssetNumber}?since_index=${lastLogIndex}`);
        if (!response.ok) {
            // If asset logs are not found (e.g. server restarted, or tests never started for this asset)
            // or other error, display a message and stop polling for this run.
            const errorData = await response.json().catch(() => null); // Try to get JSON error, otherwise null
            const errorMsg = errorData ? (errorData.error || `HTTP error ${response.status}`) : `HTTP error ${response.status}`;
            console.error("Error polling logs:", errorMsg);
            testStatusLog.innerHTML += `<p style="color: orange;">Could not fetch further logs: ${errorMsg}. Polling stopped.</p>`;
            stopLogPolling();
            checkRunTestButtonState(); // Re-enable button based on current valid inputs
            return;
        }

        const data = await response.json();
        if (data.logs && data.logs.length > 0) {
            data.logs.forEach(log => {
                // Sanitize log message before inserting into HTML to prevent XSS
                const P = document.createElement('p');
                P.textContent = log;
                if (log.startsWith('[ERROR]')) P.style.color = 'red';
                if (log.startsWith('[WARNING]')) P.style.color = 'orange';
                if (log.startsWith('[SUCCESS]')) P.style.color = 'green';
                testStatusLog.appendChild(P);
            });
            lastLogIndex = data.last_index;
            testStatusLog.scrollTop = testStatusLog.scrollHeight; // Auto-scroll
        }

        // Simple way to detect end of tests: Orchestrator logs "All tests completed"
        // This is a basic heuristic. A more robust solution would be a dedicated status field.
        if (data.logs && data.logs.some(log => log.includes("All tests completed"))) {
            testStatusLog.innerHTML += `<p style="color: green;">Test sequence finished. Polling stopped.</p>`;
            stopLogPolling();
            checkRunTestButtonState(); // Re-enable button
        }

    } catch (error) {
        console.error("Error during log polling:", error);
        testStatusLog.innerHTML += `<p style="color: red;">Error polling logs: ${error.message}. Polling stopped.</p>`;
        stopLogPolling();
        checkRunTestButtonState();
    }
}


function checkRunTestButtonState() {
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');

    const currentOperatorId = document.getElementById('operator-id').value;
    const currentAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;

    // Disable run button if polling is active, or if inputs are invalid
    const shouldDisable = logPollInterval || !currentOperatorId || !currentAssetNumber || !currentProfileName;

    if (runTestsBtn) runTestsBtn.disabled = shouldDisable;
    if (runTestsBtnQuick) runTestsBtnQuick.disabled = shouldDisable;
}

async function runTests() {
    const testStatusLog = document.getElementById('test-status-log');
    const testStatusSection = document.getElementById('test-status-section');

    if (!testStatusLog) return;

    const currentOperatorId = document.getElementById('operator-id').value;
    // Assign to global currentPollingAssetNumber for the poller to use
    currentPollingAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;

    if (!currentOperatorId || !currentPollingAssetNumber || !currentProfileName) {
        alert('Please ensure Operator ID, Asset Number are filled and a Profile is selected.');
        currentPollingAssetNumber = null; // Clear if invalid start
        return;
    }

    // Show test status section
    if (testStatusSection) {
        testStatusSection.style.display = 'block';
        testStatusSection.scrollIntoView({ behavior: 'smooth' });
    }

    testStatusLog.innerHTML = `<p>Starting tests for profile "${currentProfileName}" on asset "${currentPollingAssetNumber}" by operator "${currentOperatorId}"...</p>`;
    checkRunTestButtonState(); // This will disable both buttons
    stopLogPolling();      // Stop any previous polling
    lastLogIndex = -1;     // Reset log index for the new test run

    const payload = {
        asset_number: currentPollingAssetNumber,
        operator_id: currentOperatorId,
        profile_name: currentProfileName
    };

    try {
        const response = await fetch('/api/run_tests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (response.ok) {
            const P = document.createElement('p');
            P.textContent = `Test execution request successful: ${result.message}`;
            testStatusLog.appendChild(P);
            testStatusLog.innerHTML += `<p>Fetching logs...</p>`; // Initial message

            // Start polling for logs
            if (!logPollInterval) {
                logPollInterval = setInterval(pollLogs, 2000); // Poll every 2 seconds
                console.log(`Started log polling for asset ${currentPollingAssetNumber}.`);
            }
        } else {
            testStatusLog.innerHTML += `<p style="color: red;">Error starting tests: ${result.error} ${result.details || ''}</p>`;
            currentPollingAssetNumber = null; // Clear asset number on error
            checkRunTestButtonState(); // Re-evaluate button state
        }
    } catch (error) {
        console.error('Failed to run tests:', error);
        testStatusLog.innerHTML += `<p style="color: red;">Failed to send test execution request: ${error.message}</p>`;
        currentPollingAssetNumber = null; // Clear asset number on error
        checkRunTestButtonState(); // Re-evaluate button state
    }
    // Note: runTestsBtn state is now primarily managed by checkRunTestButtonState and the polling logic
}

// Visual Test Functions
function launchVisualTest(testType) {
    const assetNumber = document.getElementById('asset-number').value;
    if (!assetNumber || !assetNumber.trim()) {
        alert('Please enter an asset number first.');
        return;
    }

    // Get test parameters from UI or use defaults
    const testSizeMb = testType === 'ram' ? 1024 : null;
    const durationSeconds = testType === 'ram' ? 30 : (testType === 'cpu' ? 10 : null);

    // Build URL with parameters
    const params = new URLSearchParams({
        test_type: testType,
        asset_number: assetNumber,
        ...(testSizeMb && { test_size_mb: testSizeMb }),
        ...(durationSeconds && { duration_seconds: durationSeconds })
    });

    // Open visual test in new window
    const testWindow = window.open(
        `/visual_test?${params.toString()}`,
        'visual_test',
        'width=1024,height=768,fullscreen=yes,resizable=yes'
    );

    if (!testWindow) {
        alert('Failed to open test window. Please check your popup blocker settings.');
    }
}

// Modal Management Functions
function closeProfileModal() {
    const modal = document.getElementById('profile-modal');
    if (modal) modal.style.display = 'none';
}

function openDeviceConditionsModal() {
    const modal = document.getElementById('device-conditions-modal');
    const assetNumber = document.getElementById('asset-number').value;

    if (!assetNumber) {
        alert('Please enter an asset number first.');
        return;
    }

    if (modal) {
        modal.style.display = 'block';
        // Load device conditions for the current asset
        loadDeviceConditionsInModal(assetNumber);
    }
}

async function loadDeviceConditionsInModal(assetNumber) {
    const formArea = document.getElementById('dc-form-area');
    const dcStatus = document.getElementById('dc-status');

    if (!formArea || !dcStatus) return;

    formArea.innerHTML = `<p>Loading conditions for asset: ${assetNumber}...</p>`;
    dcStatus.textContent = '';

    let conditions = await fetchData(`/api/device_conditions/${assetNumber}`);
    const structure = await fetchDefaultDeviceConditionStructureIfNeeded();

    if (!structure) {
        formArea.innerHTML = '<p style="color:red;">Error: Could not load device condition structure. Cannot build form.</p>';
        return;
    }

    if (conditions && conditions.default_structure_if_needed) {
        console.log("No specific conditions found, using default structure for new entry.");
        conditions = {};
    } else if (conditions === null || (conditions && conditions.error && !conditions.default_structure_if_needed)) {
        console.log("Error loading conditions, but attempting to show a blank form with default structure.");
        conditions = {};
    }

    if (conditions === null || conditions.error) {
        conditions = {};
    }

    buildDeviceConditionForm(conditions, structure);
}

function closeDeviceConditionsModal() {
    const modal = document.getElementById('device-conditions-modal');
    if (modal) modal.style.display = 'none';
}

function closeResultDetailsModal() {
    const modal = document.getElementById('result-details-modal');
    if (modal) modal.style.display = 'none';
}

// Clear test log function
function clearTestLog() {
    const testStatusLog = document.getElementById('test-status-log');
    const testStatusSection = document.getElementById('test-status-section');

    if (testStatusLog) {
        testStatusLog.innerHTML = '';
    }
    if (testStatusSection) {
        testStatusSection.style.display = 'none';
    }
}

// Add clear log button handler
document.addEventListener('DOMContentLoaded', () => {
    const clearLogBtn = document.getElementById('clear-log-btn');
    if (clearLogBtn) {
        clearLogBtn.addEventListener('click', clearTestLog);
    }
});

// --- Results Viewer Functions ---
async function listResultsForAsset() {
    const assetNumInput = document.getElementById('results-asset-number');
    const resultsListDiv = document.getElementById('results-list');
    const resultDetailsViewDiv = document.getElementById('result-details-view');

    if (!assetNumInput || !resultsListDiv || !resultDetailsViewDiv) return;

    const assetNum = assetNumInput.value.trim();
    if (!assetNum) {
        alert("Please enter an Asset Number to list results.");
        resultsListDiv.innerHTML = '';
        resultDetailsViewDiv.innerHTML = '';
        return;
    }

    resultsListDiv.innerHTML = `<p>Fetching results for asset: ${assetNum}...</p>`;
    resultDetailsViewDiv.innerHTML = ''; // Clear previous details

    const data = await fetchData(`/api/results/${assetNum}`);
    if (!data) {
        resultsListDiv.innerHTML = `<p>Could not fetch results for asset ${assetNum}. Server error or no results found.</p>`;
        return;
    }

    if (data.error) {
         resultsListDiv.innerHTML = `<p>Error: ${data.error}</p>`;
         return;
    }

    let html = `<h4>Available Results for ${assetNum}:</h4>`;
    const allResults = (data.consolidated_results || []).concat(data.individual_results || []);

    if (allResults.length === 0) {
        html += '<p>No result files found for this asset number.</p>';
    } else {
        html += '<ul>';
        allResults.forEach(fileName => {
            html += `<li><a href="#" data-asset="${assetNum}" data-filename="${fileName}" class="result-file-link">${fileName}</a></li>`;
        });
        html += '</ul>';
    }
    resultsListDiv.innerHTML = html;

    // Add event listeners to the new links
    document.querySelectorAll('.result-file-link').forEach(link => {
        link.addEventListener('click', displaySpecificResult);
    });
}

async function displaySpecificResult(event) {
    event.preventDefault();
    const assetNum = event.target.dataset.asset;
    const fileName = event.target.dataset.filename;
    const modal = document.getElementById('result-details-modal');
    const resultDetailsViewDiv = document.getElementById('result-details-view');
    const resultDetailsTitle = document.getElementById('result-details-title');

    if (!assetNum || !fileName || !resultDetailsViewDiv || !modal) return;

    // Show modal and update title
    modal.style.display = 'block';
    if (resultDetailsTitle) {
        resultDetailsTitle.textContent = `Result: ${fileName}`;
    }

    resultDetailsViewDiv.innerHTML = `<p>Fetching ${fileName} for asset ${assetNum}...</p>`;

    // The API endpoint /api/results/<asset_number>/<result_filename> sends the file content directly.
    try {
        const response = await fetch(`/api/results/${assetNum}/${fileName}`);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({error: `HTTP error ${response.status}`}));
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }

        const resultText = await response.text();

        let contentToDisplay;
        try {
            // Try to parse as JSON and pretty-print
            const resultJson = JSON.parse(resultText);
            contentToDisplay = `<pre>${JSON.stringify(resultJson, null, 2)}</pre>`;
        } catch (e) {
            // If not JSON, display as plain text
            const P = document.createElement('pre');
            P.textContent = resultText;
            contentToDisplay = P.outerHTML;
        }
        resultDetailsViewDiv.innerHTML = contentToDisplay;

    } catch (error) {
        console.error(`Error fetching specific result ${fileName}:`, error);
        resultDetailsViewDiv.innerHTML = `<p style="color: red;">Error loading result file ${fileName}: ${error.message}</p>`;
    }
}

// Hook up Results Viewer button in DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
    // ... existing listeners ...
    const listResultsBtn = document.getElementById('list-results-btn');
    if (listResultsBtn) {
        listResultsBtn.addEventListener('click', listResultsForAsset);
    }
    // Also allow pressing Enter in the asset number input field
    const resultsAssetNumberInput = document.getElementById('results-asset-number');
    if (resultsAssetNumberInput) {
        resultsAssetNumberInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault(); // Prevent form submission if it were in a form
                listResultsForAsset();
            }
        });
    }

    // Device Conditions
    const loadDcBtn = document.getElementById('load-dc-btn');
    if (loadDcBtn) {
        loadDcBtn.addEventListener('click', loadDeviceConditions);
    }
    const saveDcBtn = document.getElementById('save-dc-btn');
    if (saveDcBtn) {
        saveDcBtn.addEventListener('click', saveDeviceConditions);
    }
    const dcAssetNumberInput = document.getElementById('dc-asset-number');
    if (dcAssetNumberInput) {
        dcAssetNumberInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                loadDeviceConditions();
            }
        });
         // When asset number changes, clear old form and hide save button
        dcAssetNumberInput.addEventListener('input', () => {
            document.getElementById('dc-form-area').innerHTML = '<p>Load or enter an asset number to manage device conditions.</p>';
            if(saveDcBtn) saveDcBtn.style.display = 'none';
            document.getElementById('dc-status').textContent = '';
        });
    }
});

// --- Device Condition Functions ---
// This is the expected structure, including all possible categories and items.
// Fetched from DEFAULT_DEVICE_CONDITIONS_STRUCTURE in the backend if no conditions exist for an asset.
let defaultDeviceConditionsStructure = null;

async function fetchDefaultDeviceConditionStructureIfNeeded() {
    if (!defaultDeviceConditionsStructure) {
        // This is a bit of a workaround. The default structure is part of the 404 response
        // when conditions for an asset are not found. We'll make a dummy call for a non-existent asset
        // if we haven't fetched it yet. A dedicated endpoint would be cleaner.
        // Let's assume for now the backend sends it or we pre-populate it.
        // For robustness, an actual endpoint /api/device_conditions_structure would be better.
        // For now, if the first load fails and gives us the structure, we store it.
        console.log("Attempting to get default device condition structure if not already loaded.");
        // This call is just to potentially get the default structure from a 404.
        // It's not ideal. A dedicated endpoint /api/device_conditions/structure would be better.
        const tempAssetForStructure = "___FETCH_STRUCTURE___";
        const data = await fetchData(`/api/device_conditions/${tempAssetForStructure}`);
        if (data && data.default_structure_if_needed) {
            defaultDeviceConditionsStructure = data.default_structure_if_needed;
            console.log("Default device condition structure loaded:", defaultDeviceConditionsStructure);
        } else if (!defaultDeviceConditionsStructure) {
            // Fallback if the above method doesn't work or if we want a hardcoded default to ensure UI can render
            defaultDeviceConditionsStructure = {
                "Cosmetic": {
                    "Screen": "Good", "Housing": "Good", "Keyboard": "Good", "Touchpad": "Good", "Palmrest": "Good"
                },
                "Functionality": {
                    "Ports": "Working", "Webcam": "Working", "Speakers": "Working", "Mic": "Working"
                },
                "Other":{
                    "BIOS_Password": "Not Set", "Computrace": "Not Active"
                },
                "Notes": "" // A single string for general notes
            };
            console.warn("Using hardcoded fallback default device condition structure.");
        }
    }
    return defaultDeviceConditionsStructure;
}


function buildDeviceConditionForm(conditions, structure) {
    const formArea = document.getElementById('dc-form-area');
    formArea.innerHTML = ''; // Clear previous form

    for (const category in structure) {
        const categoryDiv = document.createElement('div');
        categoryDiv.classList.add('dc-category');

        const categoryTitle = document.createElement('h4');
        categoryTitle.textContent = category;
        categoryDiv.appendChild(categoryTitle);

        if (category === "Notes") { // Special handling for Notes string
            const notesTextarea = document.createElement('textarea');
            notesTextarea.id = `dc-input-Notes`;
            notesTextarea.name = `Notes`;
            notesTextarea.value = conditions[category] || structure[category] || "";
            notesTextarea.rows = 3;
            notesTextarea.style.width = "90%";

            const label = document.createElement('label');
            label.htmlFor = notesTextarea.id;
            label.textContent = "Notes:";
            label.style.display = "block";

            categoryDiv.appendChild(label);
            categoryDiv.appendChild(notesTextarea);
        } else {
            for (const item in structure[category]) {
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('dc-item');

                const label = document.createElement('label');
                label.htmlFor = `dc-input-${category}-${item}`;
                label.textContent = `${item}:`;

                const input = document.createElement('input');
                input.type = 'text';
                input.id = `dc-input-${category}-${item}`;
                input.name = `${category}-${item}`;
                // Value comes from existing conditions, or the default in structure, or empty
                input.value = (conditions[category] && conditions[category][item] !== undefined) ? conditions[category][item] : (structure[category][item] || "");

                itemDiv.appendChild(label);
                itemDiv.appendChild(input);
                categoryDiv.appendChild(itemDiv);
            }
        }
        formArea.appendChild(categoryDiv);
    }
    document.getElementById('save-dc-btn').style.display = 'inline-block'; // Show save button
}

async function loadDeviceConditions() {
    const assetNumInput = document.getElementById('dc-asset-number');
    const formArea = document.getElementById('dc-form-area');
    const dcStatus = document.getElementById('dc-status');
    const saveBtn = document.getElementById('save-dc-btn');

    if (!assetNumInput || !formArea || !dcStatus || !saveBtn) return;
    const assetNum = assetNumInput.value.trim();

    if (!assetNum) {
        alert("Please enter an Asset Number to load conditions.");
        formArea.innerHTML = '<p>Load or enter an asset number to manage device conditions.</p>';
        saveBtn.style.display = 'none';
        dcStatus.textContent = '';
        return;
    }

    formArea.innerHTML = `<p>Loading conditions for asset: ${assetNum}...</p>`;
    dcStatus.textContent = '';
    saveBtn.style.display = 'none';

    let conditions = await fetchData(`/api/device_conditions/${assetNum}`);
    const structure = await fetchDefaultDeviceConditionStructureIfNeeded();

    if (!structure) {
        formArea.innerHTML = '<p style="color:red;">Error: Could not load device condition structure. Cannot build form.</p>';
        return;
    }

    if (conditions && conditions.default_structure_if_needed) {
        // This means no specific conditions were found, and API returned the default structure hint
        console.log("No specific conditions found, using default structure for new entry.");
        conditions = {}; // Start with empty conditions, form will use defaults from structure
    } else if (conditions === null || (conditions && conditions.error && !conditions.default_structure_if_needed)) {
        // Actual error or null response without structure hint
        formArea.innerHTML = `<p style="color:red;">Error loading conditions for ${assetNum}. Check console.</p>`;
        // Still, try to build a blank form using the default structure if available
        // This allows creating new conditions even if initial load fails.
        console.log("Error loading conditions, but attempting to show a blank form with default structure.");
        conditions = {}; // Treat as new/empty
    }

    // If conditions is null (e.g. network error from fetchData) or an error object from API
    // we want to ensure it's an empty object so form populates with defaults from structure.
    if (conditions === null || conditions.error) {
        conditions = {};
    }

    buildDeviceConditionForm(conditions, structure);
}

async function saveDeviceConditions() {
    const dcStatus = document.getElementById('dc-status');
    const assetNumber = document.getElementById('asset-number').value;

    if (!assetNumber) {
        alert("Asset Number is missing. Cannot save conditions.");
        return;
    }

    const structure = await fetchDefaultDeviceConditionStructureIfNeeded();
    if (!structure) {
        if (dcStatus) {
            dcStatus.textContent = 'Error: Device condition structure not available. Cannot save.';
            dcStatus.className = 'status-message error';
        }
        return;
    }

    const conditionsToSave = {};
    for (const category in structure) {
        if (category === "Notes") {
            const notesTextarea = document.getElementById(`dc-input-Notes`);
            conditionsToSave[category] = notesTextarea ? notesTextarea.value : "";
        } else {
            conditionsToSave[category] = {};
            for (const item in structure[category]) {
                const inputElement = document.getElementById(`dc-input-${category}-${item}`);
                if (inputElement) {
                    conditionsToSave[category][item] = inputElement.value;
                } else {
                    console.warn(`Input element for ${category}-${item} not found!`);
                    conditionsToSave[category][item] = structure[category][item];
                }
            }
        }
    }

    if (dcStatus) {
        dcStatus.textContent = 'Saving...';
        dcStatus.className = 'status-message info';
    }

    const response = await fetch(`/api/device_conditions/${assetNumber}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(conditionsToSave)
    });

    const result = await response.json();
    if (dcStatus) {
        if (response.ok) {
            dcStatus.textContent = result.message || 'Conditions saved successfully!';
            dcStatus.className = 'status-message success';

            // Update quick status indicator
            const dcStatusQuick = document.getElementById('dc-status-quick');
            if (dcStatusQuick) {
                dcStatusQuick.textContent = 'Saved';
                dcStatusQuick.className = 'status-indicator success';
            }

            // Auto-close modal after 2 seconds
            setTimeout(() => {
                closeDeviceConditionsModal();
            }, 2000);
        } else {
            dcStatus.textContent = `Error: ${result.error || 'Failed to save conditions.'}`;
            dcStatus.className = 'status-message error';
        }
    }
}

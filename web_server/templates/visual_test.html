<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Test - Nexus Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #1E1E1E;
            color: #FFFFFF;
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .test-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            justify-content: center;
            align-items: center;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .system-info, .test-info {
            font-size: 12px;
            color: #FFFFFF;
            margin-bottom: 10px;
        }

        .progress-section {
            width: 100%;
            max-width: 800px;
            margin-bottom: 30px;
        }

        .progress-label {
            font-size: 12px;
            color: #FFFFFF;
            margin-bottom: 5px;
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #333;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background-color: #5565f7;
            width: 0%;
            transition: width 0.3s ease;
        }

        .operation-text {
            font-size: 10px;
            color: #AAAAAA;
            margin-bottom: 5px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
            max-width: 600px;
            margin-bottom: 30px;
        }

        .stat-item {
            font-size: 12px;
            color: #FFFFFF;
            text-align: left;
        }

        .memory-visualization {
            width: 800px;
            height: 200px;
            background-color: #2D2D2D;
            border-radius: 4px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        /* LCD Test Specific Styles */
        .lcd-test-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .lcd-color-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            padding: 20px;
            border-radius: 8px;
            background-color: rgba(128, 128, 128, 0.8);
            color: #333;
            z-index: 10;
        }

        .lcd-controls {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 10;
        }

        .lcd-btn {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lcd-btn-ok {
            background-color: #28a745;
            color: white;
        }

        .lcd-btn-ok:hover {
            background-color: #218838;
        }

        .lcd-btn-fail {
            background-color: #dc3545;
            color: white;
        }

        .lcd-btn-fail:hover {
            background-color: #c82333;
        }

        .lcd-progress {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: #666;
            font-size: 16px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            z-index: 10;
        }

        /* Hide non-LCD elements during LCD test */
        .lcd-test-active .test-container > *:not(.lcd-test-container) {
            display: none;
        }

        .controls {
            display: flex;
            gap: 15px;
        }

        .btn {
            background-color: #3256b0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #7c94f7;
        }

        .btn:disabled {
            background-color: #555;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-running {
            background-color: #28a745;
        }

        .status-complete {
            background-color: #007bff;
        }

        .status-error {
            background-color: #dc3545;
        }

        .status-stopping {
            background-color: #ffc107;
            color: #000;
        }

        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #1E1E1E;
            z-index: 9999;
            display: none;
        }

        .error-message {
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: none;
            border: none;
            color: #FFFFFF;
            font-size: 18px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 900px) {
            .memory-visualization {
                width: 100%;
                max-width: 600px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="fullscreen-overlay" id="testOverlay">
        <button class="close-btn" onclick="closeTest()" title="Close Test (ESC)">✕ Close</button>
        
        <div class="status-indicator" id="statusIndicator">
            <span id="statusText">Initializing</span>
        </div>

        <div class="test-container">
            <div class="test-header">
                <div class="test-title" id="testTitle">RAM Test</div>
                <div class="system-info" id="systemInfo">Loading system information...</div>
                <div class="test-info" id="testInfo">Test Size: 1024 MB | Duration: 30 seconds</div>
            </div>

            <div class="progress-section">
                <label class="progress-label">Overall Progress:</label>
                <div class="progress-bar">
                    <div class="progress-fill" id="overallProgress"></div>
                </div>

                <label class="progress-label">Current Operation:</label>
                <div class="operation-text" id="operationText">Initializing...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="operationProgress"></div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">Cycles: <span id="cyclesCount">0</span></div>
                <div class="stat-item">Patterns: <span id="patternsCount">0</span></div>
                <div class="stat-item">Errors: <span id="errorsCount">0</span></div>
                <div class="stat-item">Speed: <span id="speedValue">0 MB/s</span></div>
                <div class="stat-item">Time Left: <span id="timeLeftValue">0s</span></div>
                <div class="stat-item">Status: <span id="statusValue">Initializing</span></div>
            </div>

            <div class="memory-visualization" id="memoryViz">
                Memory Visualization
            </div>

            <div class="controls">
                <button class="btn" id="startBtn" onclick="startTest()" disabled>Start Test</button>
                <button class="btn btn-danger" id="stopBtn" onclick="stopTest()">Stop Test</button>
                <button class="btn" onclick="closeTest()">Close</button>
            </div>

            <div class="error-message" id="errorMessage" style="display: none;"></div>
        </div>

        <!-- LCD Test Interface -->
        <div class="lcd-test-container" id="lcdTestContainer" style="display: none;">
            <div class="lcd-progress" id="lcdProgress">Color 1 of 5</div>

            <div class="lcd-color-info" id="lcdColorInfo">
                <div id="lcdColorName">Black</div>
                <div style="font-size: 16px; margin-top: 10px;">
                    Press OK if the color displays correctly<br>
                    Press FAIL if there are issues
                </div>
            </div>

            <div class="lcd-controls">
                <button class="lcd-btn lcd-btn-ok" onclick="submitLcdInput('ok')" id="lcdOkBtn">
                    ✓ OK (Enter)
                </button>
                <button class="lcd-btn lcd-btn-fail" onclick="submitLcdInput('fail')" id="lcdFailBtn">
                    ✗ FAIL (Esc)
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let eventSource = null;
        let testParams = {};

        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                test_type: params.get('test_type') || 'ram',
                asset_number: params.get('asset_number') || 'TEST001',
                test_size_mb: parseInt(params.get('test_size_mb')) || 1024,
                duration_seconds: parseInt(params.get('duration_seconds')) || 30
            };
        }

        // Initialize test
        function initTest() {
            testParams = getUrlParams();

            // Update UI based on test type
            document.getElementById('testTitle').textContent =
                testParams.test_type.toUpperCase() + ' Test';

            if (testParams.test_type === 'lcd') {
                // Hide RAM/CPU specific info for LCD test
                document.getElementById('testInfo').style.display = 'none';
                // Show LCD test interface
                setupLcdTest();
            } else {
                document.getElementById('testInfo').textContent =
                    `Test Size: ${testParams.test_size_mb} MB | Duration: ${testParams.duration_seconds} seconds`;
            }

            // Show overlay
            document.getElementById('testOverlay').style.display = 'block';

            // Auto-start test
            setTimeout(startTest, 500);
        }

        // Setup LCD test interface
        function setupLcdTest() {
            const testContainer = document.querySelector('.test-container');
            const lcdContainer = document.getElementById('lcdTestContainer');

            // Hide regular test interface and show LCD interface
            testContainer.classList.add('lcd-test-active');
            lcdContainer.style.display = 'flex';

            // Set initial background color
            lcdContainer.style.backgroundColor = '#000000';
        }

        // Start test
        async function startTest() {
            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        test_type: testParams.test_type,
                        asset_number: testParams.asset_number,
                        test_params: {
                            test_size_mb: testParams.test_size_mb,
                            duration_seconds: testParams.duration_seconds
                        }
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    currentSessionId = result.session_id;
                    connectToProgressStream();
                    
                    // Update UI
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                } else {
                    showError('Failed to start test: ' + result.error);
                }
            } catch (error) {
                showError('Failed to start test: ' + error.message);
            }
        }

        // Connect to progress stream
        function connectToProgressStream() {
            if (!currentSessionId) return;
            
            eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);
            
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateProgress(data);
            };
            
            eventSource.onerror = function(event) {
                console.error('EventSource failed:', event);
                showError('Connection to test progress lost');
            };
        }

        // Update progress display
        function updateProgress(data) {
            if (data.error) {
                showError(data.error);
                return;
            }

            const progress = data.progress;
            const status = data.status;

            // Update status
            updateStatus(status);

            if (testParams.test_type === 'lcd') {
                // Update LCD test interface
                updateLcdProgress(progress);
            } else {
                // Update progress bars for RAM/CPU tests
                document.getElementById('overallProgress').style.width = progress.overall_progress + '%';
                document.getElementById('operationProgress').style.width = progress.operation_progress + '%';

                // Update text fields
                document.getElementById('operationText').textContent = progress.operation_text;
                document.getElementById('cyclesCount').textContent = progress.cycles;
                document.getElementById('patternsCount').textContent = progress.patterns;
                document.getElementById('errorsCount').textContent = progress.errors;
                document.getElementById('speedValue').textContent = progress.speed.toFixed(1) + ' MB/s';
                document.getElementById('timeLeftValue').textContent = progress.time_left.toFixed(1) + 's';
                document.getElementById('statusValue').textContent = status;

                // Update system info if available
                if (progress.system_info && progress.system_info.total_mb) {
                    document.getElementById('systemInfo').textContent =
                        `System Memory: ${progress.system_info.total_mb} MB total, ${progress.system_info.available_mb} MB available`;
                }
            }

            // Handle test completion
            if (data.test_complete) {
                handleTestComplete(data.test_result);
            }
        }

        // Update LCD test progress
        function updateLcdProgress(progress) {
            const lcdContainer = document.getElementById('lcdTestContainer');
            const lcdProgress = document.getElementById('lcdProgress');
            const lcdColorInfo = document.getElementById('lcdColorInfo');
            const lcdColorName = document.getElementById('lcdColorName');
            const lcdOkBtn = document.getElementById('lcdOkBtn');
            const lcdFailBtn = document.getElementById('lcdFailBtn');

            // Update background color
            if (progress.current_color_hex) {
                lcdContainer.style.backgroundColor = progress.current_color_hex;
            }

            // Update progress text
            if (progress.total_colors > 0) {
                lcdProgress.textContent = `Color ${progress.color_index + 1} of ${progress.total_colors}`;
            }

            // Update color name
            if (progress.current_color) {
                lcdColorName.textContent = progress.current_color;
            }

            // Enable/disable buttons based on whether we're awaiting input
            const awaitingInput = progress.awaiting_user_input;
            lcdOkBtn.disabled = !awaitingInput;
            lcdFailBtn.disabled = !awaitingInput;

            // Adjust text color for visibility
            const colorInfo = document.getElementById('lcdColorInfo');
            if (progress.current_color_hex === '#000000') {
                colorInfo.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                colorInfo.style.color = '#333';
            } else {
                colorInfo.style.backgroundColor = 'rgba(128, 128, 128, 0.8)';
                colorInfo.style.color = '#333';
            }
        }

        // Update status indicator
        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            // Remove all status classes
            indicator.className = 'status-indicator';
            
            // Add appropriate status class
            switch (status) {
                case 'running':
                    indicator.classList.add('status-running');
                    statusText.textContent = 'Running';
                    break;
                case 'complete':
                    indicator.classList.add('status-complete');
                    statusText.textContent = 'Complete';
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    statusText.textContent = 'Error';
                    break;
                case 'stopping':
                    indicator.classList.add('status-stopping');
                    statusText.textContent = 'Stopping';
                    break;
                default:
                    statusText.textContent = 'Initializing';
            }
        }

        // Handle test completion
        function handleTestComplete(result) {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            if (result) {
                const message = result.status === 'PASS' ? 
                    'Test completed successfully!' : 
                    `Test failed: ${result.notes}`;
                
                // Auto-close after 3 seconds for successful tests
                if (result.status === 'PASS') {
                    setTimeout(() => {
                        closeTest();
                    }, 3000);
                }
            }
        }

        // Stop test
        async function stopTest() {
            if (!currentSessionId) return;
            
            try {
                await fetch(`/api/visual_test/stop/${currentSessionId}`, {
                    method: 'POST'
                });
                
                document.getElementById('stopBtn').disabled = true;
            } catch (error) {
                console.error('Failed to stop test:', error);
            }
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Close test
        function closeTest() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            if (currentSessionId) {
                // Try to stop the test
                stopTest();
            }
            
            // Close window or redirect
            if (window.opener) {
                window.close();
            } else {
                window.location.href = '/';
            }
        }

        // Submit LCD test input
        async function submitLcdInput(action) {
            if (!currentSessionId) return;

            try {
                const response = await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: action })
                });

                const result = await response.json();

                if (!response.ok) {
                    showError('Failed to submit input: ' + result.error);
                }
            } catch (error) {
                showError('Failed to submit input: ' + error.message);
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                if (testParams.test_type === 'lcd') {
                    submitLcdInput('fail');
                } else {
                    closeTest();
                }
            } else if (event.key === 'Enter') {
                if (testParams.test_type === 'lcd') {
                    submitLcdInput('ok');
                }
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initTest);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nexus Agent</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
</head>
<body>
    <header>
        <h1>Nexus Agent</h1>
        <!-- Consolidated Asset Information Header -->
        <div class="asset-header">
            <div class="asset-input-group">
                <label for="operator-id">Operator ID:</label>
                <input type="text" id="operator-id" name="operator-id" placeholder="Enter operator ID" autofocus>
            </div>
            <div class="asset-input-group">
                <label for="asset-number">Asset Number:</label>
                <input type="text" id="asset-number" name="asset-number" placeholder="Enter asset number">
            </div>
            <div class="asset-status" id="asset-status">
                <span id="asset-status-text">Enter asset information to begin</span>
            </div>
        </div>
    </header>
    <main>
        <!-- Main Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Test Profile & Execution -->
            <section id="test-profile-section" class="dashboard-card priority-card">
                <h2>Test Profile</h2>
                <div class="test-profile-content">
                    <div class="profile-selection">
                        <label for="profile-select-quick">Select Profile:</label>
                        <select id="profile-select-quick" name="profile-select-quick" class="profile-select">
                            <option value="">-- Select Profile --</option>
                        </select>
                        <button id="load-profile-details-quick" class="btn-secondary btn-compact">View Details</button>
                    </div>
                    <div id="profile-details-quick" class="profile-details-preview">
                        <!-- Profile details preview will appear here -->
                    </div>
                    <div class="test-execution-controls">
                        <button id="run-tests-btn-quick" class="btn-primary btn-large" disabled>
                            <span class="btn-icon">▶</span> Run Tests
                        </button>
                    </div>
                </div>
            </section>

            <!-- System Information - Compact -->
            <section id="system-info" class="dashboard-card">
                <h2>System Info</h2>
                <div id="system-info-content" class="compact-info">
                    <p>Loading...</p>
                </div>
            </section>

            <!-- Device Conditions - Integrated -->
            <section id="device-conditions" class="dashboard-card">
                <h2>Device Conditions</h2>
                <div class="device-conditions-compact">
                    <button id="load-dc-btn-quick" class="btn-secondary">Load/Edit Conditions</button>
                    <div id="dc-status-quick" class="status-indicator"></div>
                </div>
            </section>
        </div>

        <!-- Secondary Actions Row -->
        <div class="secondary-actions">
            <!-- Visual Tests - Standalone -->
            <section id="visual-tests" class="dashboard-card">
                <h2>Visual Tests</h2>
                <div class="visual-tests-content">
                    <p class="visual-tests-description">Individual visual tests for specific hardware components</p>
                    <div class="visual-test-buttons">
                        <button onclick="launchVisualTest('lcd')" class="btn-secondary">LCD Display Test</button>
                        <button onclick="launchVisualTest('ram')" class="btn-secondary">RAM Test</button>
                        <button onclick="launchVisualTest('cpu')" class="btn-secondary">CPU Test</button>
                    </div>
                </div>
            </section>

            <!-- Profile Management - Compact -->
            <section id="profile-management" class="dashboard-card collapsible">
                <h2 class="collapsible-header" onclick="toggleSection('profile-management')">
                    Profile Management <span class="collapse-icon">▼</span>
                </h2>
                <div class="collapsible-content">
                    <div class="profile-quick-actions">
                        <select id="profile-select" name="profile-select" class="profile-select-main">
                            <option value="">-- Select a Profile --</option>
                        </select>
                        <button id="load-profile-details-btn" class="btn-secondary btn-compact">Details</button>
                        <button id="create-profile-btn" class="btn-secondary btn-compact">New</button>
                        <button id="edit-profile-btn" class="btn-secondary btn-compact" disabled>Edit</button>
                        <button id="delete-profile-btn" class="btn-secondary btn-compact" disabled>Delete</button>
                    </div>
                    <div id="profile-details" class="profile-details-compact">
                        <!-- Profile details will be shown here -->
                    </div>
                </div>
            </section>

            <!-- Results Viewer - Compact -->
            <section id="results-viewer" class="dashboard-card collapsible">
                <h2 class="collapsible-header" onclick="toggleSection('results-viewer')">
                    Results Viewer <span class="collapse-icon">▼</span>
                </h2>
                <div class="collapsible-content">
                    <div class="results-quick-actions">
                        <button id="list-results-btn-quick" class="btn-secondary">View Results for Current Asset</button>
                        <div class="results-alt-input">
                            <input type="text" id="results-asset-number" name="results-asset-number" placeholder="Or enter different asset number">
                            <button id="list-results-btn" class="btn-secondary btn-compact">View</button>
                        </div>
                    </div>
                    <div id="results-list" class="results-list-compact">
                        <!-- Results list will appear here -->
                    </div>
                </div>
            </section>
        </div>

        <!-- Test Status and Logs -->
        <section id="test-status-section" class="dashboard-card full-width" style="display:none;">
            <h2>Test Status</h2>
            <div id="test-status-log" class="test-log">
                <!-- Test status and logs will appear here -->
            </div>
            <button id="clear-log-btn" class="btn-secondary btn-compact">Clear Log</button>
        </section>

        <!-- Device Conditions Modal -->
        <div id="device-conditions-modal" class="modal" style="display:none;">
            <div class="modal-content modal-large">
                <span class="close-button" onclick="closeDevic a eConditionsModal()">&times;</span>
                <h3>Device Conditions Assessment</h3>
                <div id="dc-form-area" class="device-conditions-form">
                    <p>Loading device conditions...</p>
                </div>
                <div class="modal-actions">
                    <button id="save-dc-btn" class="btn-primary">Save Conditions</button>
                    <button onclick="closeDeviceConditionsModal()" class="btn-secondary">Cancel</button>
                </div>
                <div id="dc-status" class="status-message"></div>
            </div>
        </div>

        <!-- Profile Management Modal -->
        <div id="profile-modal" class="modal" style="display:none;">
            <div class="modal-content">
                <span class="close-button">&times;</span>
                <h3 id="profile-modal-title">Create Profile</h3>
                <form id="profile-form">
                    <input type="hidden" id="profile-id" name="profile-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-name">Name:</label>
                            <input type="text" id="profile-name" name="profile-name" required>
                        </div>
                        <div class="form-group">
                            <label for="profile-device-type">Device Type:</label>
                            <input type="text" id="profile-device-type" name="profile-device-type" placeholder="e.g., Laptop, Desktop">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="profile-description">Description:</label>
                        <textarea id="profile-description" name="profile-description" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <h4>Tests (select one or more):</h4>
                        <div id="profile-tests-checkboxes" class="checkbox-grid">
                            <!-- Test checkboxes will be populated here by JS -->
                        </div>
                    </div>
                    <div class="form-group">
                        <h4>Test Arguments (JSON format):</h4>
                        <textarea id="profile-test-args" name="profile-test-args" rows="4" placeholder='{"test_name_1": {"arg1": "value1"}, "test_name_2": {}}'></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" id="save-profile-btn" class="btn-primary">Save Profile</button>
                        <button type="button" onclick="closeProfileModal()" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Detail View Modal -->
        <div id="result-details-modal" class="modal" style="display:none;">
            <div class="modal-content modal-large">
                <span class="close-button" onclick="closeResultDetailsModal()">&times;</span>
                <h3 id="result-details-title">Result Details</h3>
                <div id="result-details-view" class="result-details-content">
                    <!-- Content of a selected result file will appear here -->
                </div>
                <div class="modal-actions">
                    <button onclick="closeResultDetailsModal()" class="btn-secondary">Close</button>
                </div>
            </div>
        </div>

    </main>
    <footer>
        <p>&copy; 2025 Aaron Paulina</p>
    </footer>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

import ast
import os
import sys

sys.path.insert(0, os.getcwd()) # Ensure agent modules are discoverable

MAIN_WINDOW_FILE_PATH = "agent/gui/main_window.py"
AGENT_GUI_DIR = os.path.dirname(MAIN_WINDOW_FILE_PATH)

# --- Create dummy files/directories if they don't exist ---
if not os.path.exists(AGENT_GUI_DIR):
    os.makedirs(AGENT_GUI_DIR)
    with open(os.path.join(AGENT_GUI_DIR, "__init__.py"), "w") as f:
        f.write("")

# Dummy versions for parsing if actual files don't exist
AGENT_CORE_DIR = "agent/core"
if not os.path.exists(AGENT_CORE_DIR):
    os.makedirs(AGENT_CORE_DIR)
    with open(os.path.join(AGENT_CORE_DIR, "__init__.py"), "w") as f:
        f.write("")

if not os.path.exists(os.path.join(AGENT_CORE_DIR, "result_manager.py")):
    with open(os.path.join(AGENT_CORE_DIR, "result_manager.py"), "w") as f:
        f.write("class ResultManager: pass\n")
        print("Created dummy agent/core/result_manager.py")

if not os.path.exists(os.path.join(AGENT_CORE_DIR, "test_orchestrator.py")):
    with open(os.path.join(AGENT_CORE_DIR, "test_orchestrator.py"), "w") as f:
        f.write("class TestOrchestrator: pass\n")
        print("Created dummy agent/core/test_orchestrator.py")

if not os.path.exists(MAIN_WINDOW_FILE_PATH):
    with open(MAIN_WINDOW_FILE_PATH, "w") as f_dummy_main:
        f_dummy_main.write("""
import tkinter as tk
from agent.core.result_manager import ResultManager
from agent.core.test_orchestrator import TestOrchestrator
# Minimal structure for NexusApp to be refactored

class AssetInputFrame: # Dummy for type hinting resolution by script
    def get_asset_number(self): return "dummy_asset"
    def get_operator_id(self): return "dummy_operator"

class NexusApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.asset_input_frame = AssetInputFrame() # Dummy instance
        # Old instantiation (example)
        self.result_manager = ResultManager(some_arg=True)
        self.test_orchestrator = TestOrchestrator(
            log_callback=self.log_to_gui,
            result_manager_instance=self.result_manager,
            main_app_ref=self,
            get_current_profile_callback=self.get_current_profile
            # Missing new callbacks
        )
    def log_to_gui(self, msg, level): pass
    def get_current_profile(self): return None
""")
        print(f"Created dummy {MAIN_WINDOW_FILE_PATH}")


# --- AST Transformation Logic ---
with open(MAIN_WINDOW_FILE_PATH, "r") as source_file:
    source_code = source_file.read()
tree = ast.parse(source_code)

class NexusAppTransformer(ast.NodeTransformer):
    def __init__(self):
        super().__init__()
        self.modified_rm_init = False
        self.modified_to_init = False

    def visit_ClassDef(self, node):
        if node.name == 'NexusApp':
            # Traverse methods within NexusApp class
            for i, item in enumerate(node.body):
                if isinstance(item, ast.FunctionDef) and item.name == '__init__':
                    # Transform the __init__ method directly
                    self.transform_init_method_body(item) # Pass the FunctionDef node
        return self.generic_visit(node) # Continue visiting other nodes if any

    def transform_init_method_body(self, init_method_node):
        # This method now directly modifies the body of the __init__ FunctionDef node
        new_body_stmts = []
        for stmt in init_method_node.body:
            transformed_stmt = stmt # Assume statement is kept unless modified
            if isinstance(stmt, ast.Assign):
                # Check for self.result_manager assignment
                for target in stmt.targets:
                    if isinstance(target, ast.Attribute) and target.attr == 'result_manager':
                        if isinstance(stmt.value, ast.Call) and \
                           isinstance(stmt.value.func, ast.Name) and \
                           stmt.value.func.id == 'ResultManager':
                            # Clear existing arguments for ResultManager()
                            stmt.value.args = []
                            stmt.value.keywords = []
                            self.modified_rm_init = True
                            print("Found and modified ResultManager instantiation in __init__")
                        break

            if isinstance(stmt, ast.Assign):
                # Check for self.test_orchestrator assignment
                for target in stmt.targets:
                    if isinstance(target, ast.Attribute) and target.attr == 'test_orchestrator':
                        if isinstance(stmt.value, ast.Call) and \
                           isinstance(stmt.value.func, ast.Name) and \
                           stmt.value.func.id == 'TestOrchestrator':

                            keywords_to_add = {
                                "get_asset_number_callback": ast.Attribute(
                                    value=ast.Attribute(value=ast.Name(id='self', ctx=ast.Load()), attr='asset_input_frame', ctx=ast.Load()),
                                    attr='get_asset_number', ctx=ast.Load()
                                ),
                                "get_operator_id_callback": ast.Attribute(
                                    value=ast.Attribute(value=ast.Name(id='self', ctx=ast.Load()), attr='asset_input_frame', ctx=ast.Load()),
                                    attr='get_operator_id', ctx=ast.Load()
                                )
                            }

                            current_keywords = {kw.arg: kw for kw in stmt.value.keywords}

                            for kw_name, kw_value_ast in keywords_to_add.items():
                                if kw_name in current_keywords:
                                    current_keywords[kw_name].value = kw_value_ast # Update if exists
                                else:
                                    stmt.value.keywords.append(ast.keyword(arg=kw_name, value=kw_value_ast)) # Add if new

                            self.modified_to_init = True
                            print("Found and modified TestOrchestrator instantiation in __init__")
                        break
            new_body_stmts.append(transformed_stmt)
        init_method_node.body = new_body_stmts
        # No need to return init_method_node as it's modified in place


# --- Apply transformation ---
transformer = NexusAppTransformer()
modified_tree = transformer.visit(tree) # visit returns the modified tree
ast.fix_missing_locations(modified_tree)

if not transformer.modified_rm_init:
    print("Warning: ResultManager instantiation was not found or not modified in NexusApp.__init__.")
if not transformer.modified_to_init:
    print("Warning: TestOrchestrator instantiation was not found or not modified in NexusApp.__init__.")


# --- Ensure necessary imports ---
# For this script, we'll assume ResultManager and TestOrchestrator are already imported
# as the original code would need them. Adding full import logic here if they were missing
# would be more complex than typically needed for a refactoring script of existing code.
# A production script might include it for extreme robustness.
# Example:
# from agent.core.result_manager import ResultManager
# from agent.core.test_orchestrator import TestOrchestrator
# These should already be in main_window.py if it was functional.


# --- Convert AST back to source and write ---
try:
    modified_source_code = ast.unparse(modified_tree)
except AttributeError:
    try:
        import astor
        modified_source_code = astor.to_source(modified_tree)
        print("Used astor for unparsing.")
    except ImportError:
        print("Error: ast.unparse is not available and astor is not installed.")
        exit(1)

with open(MAIN_WINDOW_FILE_PATH, "w") as source_file:
    source_file.write(modified_source_code)

print(f"Refactoring of {MAIN_WINDOW_FILE_PATH} attempted.")
if transformer.modified_rm_init:
    print("ResultManager instantiation updated (no args).")
else:
    print("ResultManager instantiation NOT updated (check script/source file).")

if transformer.modified_to_init:
    print("TestOrchestrator instantiation updated (added asset/operator callbacks).")
else:
    print("TestOrchestrator instantiation NOT updated (check script/source file).")

print("Script finished.")
EOF

import ast
import os
import sys

APP_FILE_PATH = "web_server/app.py"

# Ensure 'agent' directory is discoverable for imports.
sys.path.insert(0, os.getcwd())

# Ensure dummy agent.core.device_condition_manager.py exists if the real one is not there
DCM_MODULE_PATH = "agent/core/device_condition_manager.py"
AGENT_CORE_DIR = os.path.dirname(DCM_MODULE_PATH)
if not os.path.exists(AGENT_CORE_DIR):
    os.makedirs(AGENT_CORE_DIR)
    with open(os.path.join(AGENT_CORE_DIR, "__init__.py"), "w") as f:
        f.write("")
if not os.path.exists(DCM_MODULE_PATH):
    with open(DCM_MODULE_PATH, "w") as f_dummy_dcm:
        f_dummy_dcm.write("""
# Dummy agent.core.device_condition_manager.py
def load_device_conditions(asset_number: str): return {}
def save_device_conditions(conditions: dict, asset_number: str): return True
DEFAULT_DEVICE_CONDITIONS_STRUCTURE = {}
print("Used dummy DeviceConditionManager for script parsing.")
""")
    print(f"Created dummy {DCM_MODULE_PATH} for AST parsing.")


# New routes and necessary imports as a string block
device_condition_endpoints_string = '''
# Assuming 'request' and 'jsonify' are already imported from flask
from agent.core.device_condition_manager import (
    load_device_conditions,
    save_device_conditions,
    DEFAULT_DEVICE_CONDITIONS_STRUCTURE
)

@app.route('/api/device_conditions/<string:asset_number>', methods=['GET'])
def get_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return jsonify({"error": "Asset number is required"}), 400

    conditions = load_device_conditions(asset_number) # This function handles file path logic
    if conditions:
        return jsonify(conditions), 200
    else:
        # load_device_conditions returns {} if not found or error
        return jsonify({"message": "No conditions found for this asset, or error loading.",
                        "default_structure_if_needed": DEFAULT_DEVICE_CONDITIONS_STRUCTURE}), 404

@app.route('/api/device_conditions/<string:asset_number>', methods=['POST'])
def save_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return jsonify({"error": "Asset number is required"}), 400

    if not request.json: # Ensure request body is JSON
        return jsonify({"error": "Invalid input, JSON required"}), 400

    conditions_data = request.json

    # The save_device_conditions function handles file path logic and saving
    if save_device_conditions(conditions_data, asset_number):
        return jsonify({"message": "Device conditions saved successfully"}), 200
    else:
        # save_device_conditions might print its own error, but a generic server error is good too
        return jsonify({"error": "Failed to save device conditions"}), 500
'''

# --- Read existing app code ---
with open(APP_FILE_PATH, "r") as source_file:
    source_code = source_file.read()
tree = ast.parse(source_code)

# --- Parse new routes ---
new_routes_ast_module = ast.parse(device_condition_endpoints_string)

# --- Safely add new imports ---
# Collect import nodes from the new_routes_ast_module
imports_from_new_routes = [
    n for n in new_routes_ast_module.body if isinstance(n, (ast.Import, ast.ImportFrom))
]

# Get existing import statements from the tree to check for duplicates
# Using ast.dump for a more reliable comparison of import structures
existing_imports_dumps = set()
for node in tree.body:
    if isinstance(node, (ast.Import, ast.ImportFrom)):
        existing_imports_dumps.add(ast.dump(node))

new_imports_to_actually_add = []
for new_import_node in imports_from_new_routes:
    if ast.dump(new_import_node) not in existing_imports_dumps:
        new_imports_to_actually_add.append(new_import_node)
        existing_imports_dumps.add(ast.dump(new_import_node)) # Add to set to avoid adding duplicates from new_routes_string itself

# Determine insertion point for new imports (after docstring and existing imports)
docstring_and_imports_end_pos = 0
if tree.body and isinstance(tree.body[0], ast.Expr) and isinstance(tree.body[0].value, (ast.Str, ast.Constant)): # Docstring check
    docstring_and_imports_end_pos = 1
    for i, node in enumerate(tree.body[1:], 1): # Start search after docstring
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            docstring_and_imports_end_pos = i + 1
        else:
            break
else: # No docstring, search from beginning
    for i, node in enumerate(tree.body):
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            docstring_and_imports_end_pos = i + 1
        else:
            break

for new_import_node in reversed(new_imports_to_actually_add): # Insert in reverse to maintain specified order
    tree.body.insert(docstring_and_imports_end_pos, new_import_node)
if new_imports_to_actually_add:
    print(f"Added {len(new_imports_to_actually_add)} new import statements.")


# --- Add route function definitions ---
existing_function_names = {node.name for node in tree.body if isinstance(node, ast.FunctionDef)}
functions_added_count = 0
for new_node in new_routes_ast_module.body:
    if isinstance(new_node, ast.FunctionDef):
        if new_node.name not in existing_function_names:
            tree.body.append(new_node)
            existing_function_names.add(new_node.name)
            functions_added_count += 1
        else:
            print(f"Note: Function {new_node.name} already exists. Skipping re-addition.")
if functions_added_count > 0:
    print(f"Added {functions_added_count} new route functions.")


# --- Convert AST back to source and write ---
try:
    modified_source_code = ast.unparse(tree)
except AttributeError:
    try:
        import astor
        modified_source_code = astor.to_source(tree)
        print("Used astor for unparsing.")
    except ImportError:
        print("Error: ast.unparse is not available and astor is not installed. Python 3.9+ or astor is required.")
        exit(1)

with open(APP_FILE_PATH, "w") as source_file:
    source_file.write(modified_source_code)

print(f"Device condition management endpoints processing for {APP_FILE_PATH} complete.")

# Final check on Flask imports (request, jsonify should be there from previous steps)
final_tree = ast.parse(modified_source_code)
flask_imports_found = []
for node in final_tree.body:
    if isinstance(node, ast.ImportFrom) and node.module == "flask":
        flask_imports_found = [a.name for a in node.names]
        break
print(f"Final Flask import check: from flask import {flask_imports_found}")

print("Script to add device condition endpoints finished.")
EOF

import ast
import os
import sys

sys.path.insert(0, os.getcwd()) # Ensure agent modules are discoverable

ORCHESTRATOR_FILE_PATH = "agent/core/test_orchestrator.py"
AGENT_CORE_DIR = os.path.dirname(ORCHESTRATOR_FILE_PATH)

# --- Create dummy files/directories if they don't exist ---
if not os.path.exists(AGENT_CORE_DIR):
    os.makedirs(AGENT_CORE_DIR)
    with open(os.path.join(AGENT_CORE_DIR, "__init__.py"), "w") as f:
        f.write("")

# Dummy Profile and ResultManager for type hint parsing if actual files don't exist
# (The script needs to parse these types for import management)
AGENT_TESTS_DIR = "agent/tests"
if not os.path.exists(AGENT_TESTS_DIR):
    os.makedirs(AGENT_TESTS_DIR)
    with open(os.path.join(AGENT_TESTS_DIR, "__init__.py"), "w") as f:
        f.write("")
if not os.path.exists(os.path.join(AGENT_TESTS_DIR, "profiles.py")):
    with open(os.path.join(AGENT_TESTS_DIR, "profiles.py"), "w") as f:
        f.write("class Profile: pass\n")
        print(f"Created dummy agent/tests/profiles.py")

if not os.path.exists(os.path.join(AGENT_CORE_DIR, "result_manager.py")):
    with open(os.path.join(AGENT_CORE_DIR, "result_manager.py"), "w") as f:
        f.write("class ResultManager: pass\n")
        print(f"Created dummy agent/core/result_manager.py")


if not os.path.exists(ORCHESTRATOR_FILE_PATH):
    # Create a basic TestOrchestrator structure if the file doesn't exist
    with open(ORCHESTRATOR_FILE_PATH, "w") as f_dummy_orch:
        f_dummy_orch.write("""
import tkinter as tk
from typing import Callable, Dict, Any, Optional
from agent.tests.profiles import Profile
from agent.core.result_manager import ResultManager

class TestOrchestrator:
    def __init__(self,
                 log_callback: Callable[[str, str], None],
                 result_manager_instance: ResultManager,
                 main_app_ref: tk.Tk, # Typically NexusApp instance
                 get_current_profile_callback: Callable[[], Optional[Profile]]
                 # Old signature, to be replaced
                 ):
        self.log_callback = log_callback
        self.result_manager_instance = result_manager_instance
        self.main_app_ref = main_app_ref
        self.get_current_profile_callback = get_current_profile_callback
        # Missing new callbacks here, init will be replaced
        self.end_screen_summary_data: Dict[str, Any] = {}

    def execute_tests(self, test_list: list, category: str):
        # Dummy method for transformation
        asset_number_dummy = "dummy_asset"
        operator_id_dummy = "dummy_op"
        test_name_dummy = "dummy_test"
        result_dummy = {}
        # Old call (example, script expects 0 or more args after result)
        # self.result_manager_instance.add_result(test_name_dummy, result_dummy)
        # A more realistic old call might be:
        self.result_manager_instance.add_result(
            test_name=test_name_dummy,
            result=result_dummy,
            # asset_number=asset_number_dummy, # These were not part of original call signature in prompt
            # operator_id=operator_id_dummy
        )
        # Also, a call to consolidate results
        # self.result_manager_instance.consolidate_results_for_asset(asset_number_dummy, operator_id_dummy)
        pass
""")
        print(f"Created dummy {ORCHESTRATOR_FILE_PATH}")


new_init_method_string = '''
def __init__(self,
             log_callback: Callable[[str, str], None],
             result_manager_instance: ResultManager,
             main_app_ref: tk.Tk, # Typically NexusApp instance
             get_current_profile_callback: Callable[[], Optional[Profile]],
             get_asset_number_callback: Callable[[], str], # New
             get_operator_id_callback: Callable[[], str]  # New
             ):
    self.log_callback = log_callback
    self.result_manager_instance = result_manager_instance
    self.main_app_ref = main_app_ref
    self.get_current_profile_callback = get_current_profile_callback
    self.get_asset_number_callback = get_asset_number_callback # New
    self.get_operator_id_callback = get_operator_id_callback   # New
    self.end_screen_summary_data: Dict[str, Any] = {}
'''

with open(ORCHESTRATOR_FILE_PATH, "r") as source_file:
    source_code = source_file.read()

tree = ast.parse(source_code)

class ResultManagerCallTransformer(ast.NodeTransformer):
    def __init__(self, get_asset_call_str, get_operator_call_str):
        super().__init__()
        self.get_asset_call_ast = ast.parse(get_asset_call_str).body[0].value
        self.get_operator_call_ast = ast.parse(get_operator_call_str).body[0].value
        self.modified_calls_count = 0

    def visit_Call(self, node):
        # Check for self.result_manager_instance.add_result(*args, **kwargs)
        if isinstance(node.func, ast.Attribute) and \
           isinstance(node.func.value, ast.Attribute) and \
           node.func.value.attr == 'result_manager_instance' and \
           isinstance(node.func.value.value, ast.Name) and \
           node.func.value.value.id == 'self' and \
           node.func.attr == 'add_result':

            # Original call structure: add_result(self, test_name: str, result: Dict[str, Any])
            # New call structure: add_result(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any])
            # Prepend the new arguments: self.get_asset_number_callback(), self.get_operator_id_callback()

            # If original call used keywords, they need to be preserved.
            # If original call used positional, new args are prepended.
            # The prompt implies the old call was: add_result(test_name, result_dict)
            # So, we prepend to positional arguments.

            new_args = [self.get_asset_call_ast, self.get_operator_call_ast] + node.args
            # Check if the first two original args were keyword args for asset_number or operator_id
            # This is unlikely based on the problem description (refactoring FROM a state where they were NOT passed)

            node.args = new_args
            # Clear keywords if the old call might have used them for test_name/result, and now they'd be positional
            # For safety, if the old call was add_result(test_name="foo", result={}), this might need care.
            # But given the refactor, we assume old positional args for test_name, result.
            self.modified_calls_count +=1
            print(f"Modified call to add_result. New args: {[type(a) for a in node.args]}")

        # No change needed for consolidate_results_for_asset calls as per analysis in prompt.
        # The variables used in those calls will now correctly pick up values from the new callbacks
        # due to changes in how asset_number and operator_id are fetched within execute_tests or similar methods.
        return self.generic_visit(node)

# --- Main script logic ---
orchestrator_class_node = None
for item in tree.body:
    if isinstance(item, ast.ClassDef) and item.name == "TestOrchestrator":
        orchestrator_class_node = item
        break

if not orchestrator_class_node:
    print(f"Error: Class TestOrchestrator not found in {ORCHESTRATOR_FILE_PATH}")
    exit(1)

# 1. Replace __init__ method
init_replaced = False
for i, class_member_node in enumerate(orchestrator_class_node.body):
    if isinstance(class_member_node, ast.FunctionDef) and class_member_node.name == "__init__":
        new_init_ast = ast.parse(new_init_method_string).body[0]
        new_init_ast.decorator_list = class_member_node.decorator_list # Preserve decorators
        orchestrator_class_node.body[i] = new_init_ast
        init_replaced = True
        print(f"Successfully replaced __init__ in TestOrchestrator.")
        break
if not init_replaced:
    print(f"Warning: __init__ method in TestOrchestrator not found, so not replaced.")


# 2. Transform calls to add_result within the TestOrchestrator class
transformer = ResultManagerCallTransformer('self.get_asset_number_callback()',
                                           'self.get_operator_id_callback()')
transformer.visit(orchestrator_class_node) # Apply transformations to the class
if transformer.modified_calls_count > 0:
    print(f"Successfully modified {transformer.modified_calls_count} calls to add_result.")
else:
    print(f"Warning: No calls to add_result were found or modified. Check TestOrchestrator structure.")


# 3. Ensure necessary imports
imports_to_ensure = {
    "typing": {"Callable", "Dict", "Any", "Optional"},
    "agent.tests.profiles": {"Profile"},
    "agent.core.result_manager": {"ResultManager"}
    # tkinter import is handled separately due to 'as tk'
}

# Separate imports from the rest of the body
docstring_node = None
if tree.body and isinstance(tree.body[0], ast.Expr) and isinstance(tree.body[0].value, (ast.Str, ast.Constant)):
    docstring_node = tree.body[0]
    existing_body_nodes = tree.body[1:]
else:
    existing_body_nodes = tree.body

existing_import_nodes = [n for n in existing_body_nodes if isinstance(n, (ast.Import, ast.ImportFrom))]
other_nodes = [n for n in existing_body_nodes if not isinstance(n, (ast.Import, ast.ImportFrom))]

final_imports = list(existing_import_nodes) # Start with what's there

# Ensure 'import tkinter as tk'
tk_imported_correctly = False
for imp_node in final_imports:
    if isinstance(imp_node, ast.Import):
        for alias in imp_node.names:
            if alias.name == "tkinter" and alias.asname == "tk":
                tk_imported_correctly = True
                break
    if tk_imported_correctly: break
if not tk_imported_correctly:
    # Remove any other forms of tkinter import for simplicity before adding ours
    final_imports = [imp for imp in final_imports if not (isinstance(imp, ast.Import) and any(a.name == "tkinter" for a in imp.names))]
    final_imports.append(ast.Import(names=[ast.alias(name="tkinter", asname="tk")]))
    print("Added/Ensured 'import tkinter as tk'")


# Ensure other specified imports
for module_name, names_to_ensure in imports_to_ensure.items():
    found_module_import = False
    for imp_node in final_imports:
        if isinstance(imp_node, ast.ImportFrom) and imp_node.module == module_name:
            found_module_import = True
            current_names = {alias.name for alias in imp_node.names}
            for name in names_to_ensure:
                if name not in current_names:
                    imp_node.names.append(ast.alias(name=name, asname=None))
            imp_node.names.sort(key=lambda x: x.name) # Sort for consistency
            break
        elif isinstance(imp_node, ast.Import) and not names_to_ensure: # e.g. 'import os'
             if any(alias.name == module_name for alias in imp_node.names):
                found_module_import = True
                break

    if not found_module_import:
        if names_to_ensure:
            final_imports.append(ast.ImportFrom(module=module_name,
                                 names=[ast.alias(name=n, asname=None) for n in sorted(list(names_to_ensure))],
                                 level=0))
        else:
            final_imports.append(ast.Import(names=[ast.alias(name=module_name, asname=None)]))
        print(f"Added import for: {module_name} with names {names_to_ensure or 'N/A'}")


# Reconstruct the module body
new_tree_body = []
if docstring_node:
    new_tree_body.append(docstring_node)
new_tree_body.extend(final_imports)
new_tree_body.extend(other_nodes)
tree.body = new_tree_body

# --- Convert AST back to source and write ---
try:
    modified_source_code = ast.unparse(tree)
except AttributeError:
    try:
        import astor
        modified_source_code = astor.to_source(tree)
        print("Used astor for unparsing.")
    except ImportError:
        print("Error: ast.unparse is not available and astor is not installed.")
        exit(1)

with open(ORCHESTRATOR_FILE_PATH, "w") as source_file:
    source_file.write(modified_source_code)

print(f"Successfully refactored {ORCHESTRATOR_FILE_PATH}.")
print("Script finished.")
EOF

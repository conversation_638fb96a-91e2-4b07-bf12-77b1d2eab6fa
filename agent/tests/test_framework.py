#!/usr/bin/env python3
"""
Arcoa Nexus Test Framework

This module provides a standardized framework for implementing and running hardware tests.
It ensures consistent test execution, logging, and result formatting across all test types.
"""
import datetime
import functools
import inspect
import logging
import os
import time
import traceback
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
import pkgutil # Added for test discovery
import importlib # Added for test discovery
import platform # For OS-specific logic
import unittest  # For SkipTest handling

# Configure logging
logger = logging.getLogger("arcoa.tests")

_available_tests_registry: List[Dict[str, Any]] = []

class TestStatus(str, Enum):
    """Standardized test status values."""
    PASS = "pass"
    FAIL = "fail"
    SKIPPED = "skipped"
    ERROR = "error"  # For unexpected errors during test execution
    NOT_APPLICABLE = "not_applicable" # For elements not present or not tested


class TestSeverity(str, Enum):
    """Test severity levels to categorize tests."""
    CRITICAL = "critical"  # Test failure means device is unusable
    HIGH = "high"          # Test failure indicates major functionality issue
    MEDIUM = "medium"      # Test failure affects some functionality
    LOW = "low"            # Test failure is minor or cosmetic


class TestCategory(str, Enum):
    """Categories of tests for organization."""
    SYSTEM = "system"
    CPU = "cpu"
    MEMORY = "memory"
    STORAGE = "storage"
    DISPLAY = "display"
    INPUT = "input"
    NETWORK = "network"
    BATTERY = "battery"
    SECURITY = "security"
    WIPE = "wipe"
    INTEGRATION = "integration"


class TestResult:
    """Standardized test result container."""

    def __init__(
        self,
        test_name: str,
        status: TestStatus,
        notes: str = "",
        details: Optional[Dict[str, Any]] = None,
        started_at: Optional[datetime.datetime] = None,
        finished_at: Optional[datetime.datetime] = None
    ):
        self.test_name = test_name
        self.status = status
        self.notes = notes
        self.details = details or {}
        self.started_at = started_at or datetime.datetime.now()
        self.finished_at = finished_at or datetime.datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert test result to a dictionary for serialization."""
        return {
            "test_details": {
                "status": self.status.value if isinstance(self.status, TestStatus) else self.status,
                "notes": self.notes,
                **self.details
            },
            "started_at": self.started_at.isoformat(),
            "finished_at": self.finished_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any], test_name: str) -> 'TestResult':
        """Create a TestResult instance from a dictionary."""
        test_details = data.get("test_details", {})
        status_value = test_details.get("status", "error")

        # Convert string status to enum if needed
        try:
            status = TestStatus(status_value)
        except ValueError:
            status = TestStatus.ERROR

        # Extract notes and other details
        notes = test_details.get("notes", "")
        details = {k: v for k, v in test_details.items() if k not in ["status", "notes"]}

        # Parse timestamps
        started_at = datetime.datetime.fromisoformat(data.get("started_at", datetime.datetime.now().isoformat()))
        finished_at = datetime.datetime.fromisoformat(data.get("finished_at", datetime.datetime.now().isoformat()))

        return cls(
            test_name=test_name,
            status=status,
            notes=notes,
            details=details,
            started_at=started_at,
            finished_at=finished_at
        )


def test(
    category: TestCategory,
    severity: TestSeverity = TestSeverity.MEDIUM,
    timeout: Optional[int] = None,
    description: str = "",
    name: Optional[str] = None
) -> Callable:
    """
    Decorator for test functions that standardizes execution and result formatting.

    Args:
        category: The test category
        severity: The test severity level
        timeout: Optional timeout in seconds
        description: Human-readable description of the test
        name: Optional custom name for the test (defaults to function name)

    Returns:
        Decorated test function
    """
    def decorator(func: Callable) -> Callable:
        # Store metadata on the function
        # Store metadata in a dict
        current_test_metadata = {
            "category": category,
            "severity": severity,
            "timeout": timeout,
            "description": description or func.__doc__ or "",
            "name": name or func.__name__,
            "original_func_name": func.__name__ # Store original func name for clarity
        }
        # Attach to the original function (perhaps for other parts of the framework that might expect it)
        func.test_metadata = current_test_metadata

        # Register the test metadata globally
        module_name = func.__module__
        qualified_name = f"{module_name}.{func.__name__}"
        
        registration_metadata = {
            **current_test_metadata,
            "module": module_name,
            "qualified_name": qualified_name,
            "function_object": func # Store the actual function object
        }
        
        # Avoid duplicate registrations if modules are reloaded or imported multiple times
        if not any(t['qualified_name'] == qualified_name for t in _available_tests_registry):
            _available_tests_registry.append(registration_metadata)

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Dict[str, Any]:
            start_dt = datetime.datetime.now()
            test_name_from_metadata = current_test_metadata["name"]

            # Prepare final arguments for the test function
            final_kwargs = kwargs.copy() # Start with kwargs passed to wrapper (e.g., log_callback)

            # If 'args[0]' is a TestCase instance, try to get 'config_args' from it
            if args and hasattr(args[0], 'config_args') and isinstance(getattr(args[0], 'config_args'), dict):
                instance_config_args = getattr(args[0], 'config_args')
                # Merge, giving precedence to instance_config_args for test-specific values
                # but ensuring log_callback from original kwargs is preserved if not in instance_config_args
                merged_args = {**final_kwargs, **instance_config_args}
                # If log_callback was specifically in instance_config_args, it would override.
                # If it was only in final_kwargs (original), it's preserved.
                # If it was in neither, and test expects it, it needs a default.
                final_kwargs = merged_args

            log_callback = final_kwargs.get('log_callback', lambda msg, lvl="info": logger.info(msg))

            # Log test start
            log_callback(f"Starting test: {test_name_from_metadata}", "info")

            try:
                # Apply timeout if specified
                if timeout:
                    # TODO: Implement timeout mechanism
                    pass

                # Run the actual test with potentially merged/augmented kwargs
                result = func(*args, **final_kwargs)

                # If function returns a list (e.g., complex multi-drive result), pass through unchanged
                if isinstance(result, list):
                    return result

                # If result is already in the expected format, return it
                if isinstance(result, dict) and "test_details" in result:
                    # Add metadata if not present
                    if "metadata" not in result:
                        result["metadata"] = current_test_metadata # Use metadata from closure
                    return result

                # Otherwise, wrap the result
                if isinstance(result, tuple) and len(result) == 2:
                    # Assume (status, notes) format
                    status, notes = result
                    details = {}
                elif isinstance(result, dict):
                    # Assume dictionary with status and notes
                    status = result.get("status", TestStatus.ERROR)
                    notes = result.get("notes", "")
                    details = {k: v for k, v in result.items() if k not in ["status", "notes"]}
                else:
                    # Assume boolean result
                    status = TestStatus.PASS if result else TestStatus.FAIL
                    notes = "Test completed successfully" if result else "Test failed"
                    details = {}

            except Exception as e:
                # Handle exceptions
                status = TestStatus.ERROR
                notes = f"Test error: {str(e)}"
                details = {"traceback": traceback.format_exc()}
                log_callback(f"Test error in {test_name}: {str(e)}", "error")

            # Create standardized result
            end_dt = datetime.datetime.now()
            test_result = TestResult(
                test_name=test_name_from_metadata, # Use name from metadata
                status=status,
                notes=notes,
                details=details,
                started_at=start_dt,
                finished_at=end_dt
            ).to_dict()

            # Add metadata
            test_result["metadata"] = current_test_metadata # Use metadata from closure

            # Log completion
            duration = (end_dt - start_dt).total_seconds()
            log_callback(f"Completed test: {test_name_from_metadata} ({status}) in {duration:.2f}s", "info")

            return test_result

        # Also attach metadata to the wrapper function itself for easier external access
        wrapper.test_metadata = current_test_metadata
        return wrapper

    return decorator


def get_available_tests() -> List[Dict[str, Any]]:
    """
    Discovers and returns all available tests.

    This function iterates through Python files in the 'agent.tests' package,
    imports them to trigger the @test decorator (which registers the tests),
    and then returns the collected test metadata.
    """
    global _available_tests_registry
    logger.setLevel(logging.DEBUG) # Ensure DEBUG messages are processed for this call
    # The _available_tests_registry is populated by the @test decorator
    # when test modules are imported. We need to ensure all test modules are imported.

    # Dynamically import all modules in the 'agent.tests' package
    # to ensure their @test decorators are executed.
    import agent.tests # Ensure the package itself is known

    tests_package_path = os.path.dirname(__file__)
    
    for importer, modname, ispkg in pkgutil.walk_packages(
        path=[tests_package_path], 
        prefix='agent.tests.', 
        onerror=lambda x: None # Log or handle errors as needed
    ):
        # Ensure we don't try to import the framework itself if it's in the path,
        # or __init__.py files directly as modules to run tests from.
        logger.debug(f"[TestDiscovery] Checking module: '{modname}', Platform: '{platform.system()}', IsPkg: {ispkg}")
        if not ispkg and modname != __name__ and not modname.endswith('.__init__'):
            if modname == 'agent.tests.drive_wipe_test' and platform.system() != 'Linux':
                logger.info(f"[TestDiscovery] Conditionally SKIPPING import of Linux-specific test module '{modname}' on {platform.system()}.")
                continue
            try:
                logger.debug(f"[TestDiscovery] Attempting to import test module: '{modname}' for discovery.")
                importlib.import_module(modname)
                logger.debug(f"[TestDiscovery] Successfully imported test module: '{modname}'.")
            except unittest.SkipTest as e:
                logger.info(f"[TestDiscovery] Skipping deprecated or disabled test module '{modname}': {e}")
            except ImportError as e:
                if modname == 'agent.tests.drive_wipe_test':
                    logger.error(f"[TestDiscovery] CRITICAL IMPORT ERROR for 'agent.tests.drive_wipe_test': {e}")
                    logger.error(traceback.format_exc())
                else:
                    logger.error(f"[TestDiscovery] Failed to import test module '{modname}': {e}")
            except Exception as e:
                if modname == 'agent.tests.drive_wipe_test':
                    logger.error(f"[TestDiscovery] CRITICAL UNEXPECTED ERROR during import of 'agent.tests.drive_wipe_test': {e}")
                    logger.error(traceback.format_exc())
                else:
                    logger.error(f"[TestDiscovery] An unexpected error occurred while importing '{modname}': {e}")
                    logger.error(traceback.format_exc())

    # Sort tests by category and then by name for consistent ordering
    sorted_tests = sorted(_available_tests_registry, key=lambda t: (t.get("category", ""), t.get("name", "")))

    # Exclude deprecated battery tests from UI lists
    filtered_tests = [t for t in sorted_tests if t.get("category") != TestCategory.BATTERY]

    logger.info(
        f"Discovered {len(sorted_tests)} tests; {len(sorted_tests)-len(filtered_tests)} battery tests filtered out."
    )
    return filtered_tests


def run_test_by_name(test_name: str, **kwargs) -> Dict[str, Any]:
    """
    Run a test by its full path name.

    Args:
        test_name: Full path to the test function (e.g., "agent.tests.cpu_test.run_basic_cpu_test")
        **kwargs: Arguments to pass to the test function

    Returns:
        Test result dictionary
    """
    import importlib

    # Split the path into module and function
    try:
        module_path, func_name = test_name.rsplit(".", 1)
    except ValueError:
        logger.error(f"Invalid test name format: {test_name}. Expected 'module.function'.")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Invalid test name format: {test_name}"
        ).to_dict()

    try:
        # Import the module
        module = importlib.import_module(module_path)

        # Get the function
        func = getattr(module, func_name)

        # Run the test
        return func(**kwargs)
    except (ImportError, AttributeError) as e:
        # Handle errors
        logger.error(f"Error running test {test_name}: {e}")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Failed to run test: {str(e)}"
        ).to_dict()
    except Exception as e_exec: # Catch errors during test execution if not caught by decorator
        logger.error(f"Unexpected error executing test {test_name}: {e_exec}")
        return TestResult(
            test_name=test_name,
            status=TestStatus.ERROR,
            notes=f"Unexpected error during test execution: {str(e_exec)}",
            details={"traceback": traceback.format_exc()}
        ).to_dict()
#!/usr/bin/env python3
"""
Arcoa Nexus Test Runner

This module provides functionality to run tests in sequence or in parallel,
with detailed reporting and logging capabilities.
"""
import asyncio
import datetime
import json
import logging
import os
import sys
import time
import unittest
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from agent.tests.test_framework import (
    TestCategory, TestResult, TestSeverity, TestStatus,
    run_test_by_name # get_available_tests has been removed from the framework
    # The main() function in this script now uses unittest.discover for listing and execution.
)

# Configure logging
logger = logging.getLogger("arcoa.tests.runner")

# Helper function for metadata extraction - can be shared or duplicated from run_tests.py
# For self-containment of this script's main(), duplicating is acceptable.
def get_test_metadata_from_test_case(test_case_instance: unittest.TestCase, test_method_name: str) -> Optional[Dict[str, Any]]:
    """
    Retrieves test_metadata from a test method if it exists.
    """
    method = getattr(test_case_instance, test_method_name, None)
    if method and hasattr(method, '__func__'): # bound method
        wrapper_func = method.__func__
        if hasattr(wrapper_func, 'test_metadata'):
            return wrapper_func.test_metadata
    return None


class TestRunner:
    """Test runner for executing multiple tests with reporting."""
    
    def __init__(
        self,
        log_callback: Optional[Callable] = None,
        report_dir: Optional[str] = None,
        parallel: bool = False,
        max_workers: int = 4
    ):
        """
        Initialize the test runner.
        
        Args:
            log_callback: Optional callback for logging messages
            report_dir: Directory to save test reports
            parallel: Whether to run tests in parallel
            max_workers: Maximum number of parallel workers
        """
        self.log_callback = log_callback or (lambda msg, lvl="info": logger.info(msg))
        self.report_dir = report_dir
        self.parallel = parallel
        self.max_workers = max_workers
        self.results: Dict[str, Dict[str, Any]] = {}
        
        # Create report directory if needed
        if self.report_dir and not os.path.exists(self.report_dir):
            os.makedirs(self.report_dir)
    
    def log(self, message: str, level: str = "info") -> None:
        """Log a message using the callback or logger."""
        self.log_callback(message, level)
        
        # Also log to the standard logger
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(message)
    
    def run_tests(
        self,
        tests: List[str],
        categories: Optional[Set[TestCategory]] = None,
        severities: Optional[Set[TestSeverity]] = None,
        test_args: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Run a list of tests.
        
        Args:
            tests: List of test names to run
            categories: Optional set of categories to filter tests
            severities: Optional set of severities to filter tests
            test_args: Optional dictionary of test arguments keyed by test name
            
        Returns:
            Dictionary of test results keyed by test name
        """
        # The 'tests' argument is now expected to be a list of fully qualified unittest IDs
        # e.g., ['module.class.test_method1', 'module.class.test_method2']
        # Filtering by categories and severities based on the old framework is removed here.
        # Such filtering should be done by the caller if needed, based on the test IDs.

        if not tests:
            self.log("No tests provided to run.", "warning")
            return {}

        self.log(f"Preparing to run {len(tests)} tests (now identified by unittest ID)")
        for test_id in tests:
            self.log(f"  - {test_id}")

        # Prepare arguments for each test
        # Test_args keys should match the unittest IDs
        args_with_defaults = {}
        for test_id in tests:
            args_with_defaults[test_id] = {
                "log_callback": self.log_callback, # Ensure log_callback is passed if tests expect it via **kwargs
                **(test_args.get(test_id, {}))
            }
        
        # Run tests
        start_time = time.time()
        
        if self.parallel and len(tests) > 1:
            # Run tests in parallel
            self.results = self._run_parallel(tests, args_with_defaults)
        else:
            # Run tests sequentially
            self.results = self._run_sequential(tests, args_with_defaults)
        
        # Calculate total time
        total_time = time.time() - start_time
        
        # Log summary
        self._log_summary(total_time)
        
        # Save report if directory is specified
        if self.report_dir:
            self._save_report()
        
        return self.results
    
    def _run_sequential(
        self,
        tests: List[str],
        args_dict: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """Run tests sequentially."""
        results = {}
        
        for i, test_name in enumerate(tests):
            self.log(f"Running test {i+1}/{len(tests)}: {test_name}")
            
            try:
                # Get arguments for this test (includes log_callback and specific config from get_test_args)
                current_test_args = args_dict.get(test_name, {})

                # Run the test using unittest
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromName(test_name) # test_name is the full unittest ID
                
                # Before running, inject current_test_args onto the TestCase instance(s)
                for test_case_or_suite in suite:
                    if hasattr(test_case_or_suite, '__iter__'): # It's a TestSuite
                        for test_case in test_case_or_suite:
                            if isinstance(test_case, unittest.TestCase):
                                setattr(test_case, 'config_args', current_test_args)
                    elif isinstance(test_case_or_suite, unittest.TestCase): # It's a single TestCase
                        setattr(test_case_or_suite, 'config_args', current_test_args)
                
                internal_runner = unittest.TextTestRunner(stream=None, verbosity=0)
                test_suite_result = internal_runner.run(suite)

                # Adapt unittest.TestResult to the TestRunner's expected dictionary format
                if test_suite_result.wasSuccessful():
                    status = TestStatus.PASS
                    notes = "Test passed."
                elif test_suite_result.errors:
                    status = TestStatus.ERROR
                    # Combine error messages
                    notes = "\n".join([f"{err[0].id()}: {err[1]}" for err in test_suite_result.errors])
                elif test_suite_result.failures:
                    status = TestStatus.FAIL
                    # Combine failure messages
                    notes = "\n".join([f"{fail[0].id()}: {fail[1]}" for fail in test_suite_result.failures])
                else: # Should not happen if not successful and no errors/failures
                    status = TestStatus.UNKNOWN
                    notes = "Test finished with an unknown status."

                results[test_name] = TestResult(
                    test_name=test_name, # test_name is the unittest id
                    status=status,
                    notes=notes
                ).to_dict()

                self.log(f"Test {test_name} completed with status: {status.value}")
            except Exception as e:
                self.log(f"Error preparing or running test {test_name} with unittest: {str(e)}", "error")
                results[test_name] = TestResult(
                    test_name=test_name, # test_name is the unittest id
                    status=TestStatus.ERROR,
                    notes=f"Exception during test execution: {str(e)}"
                ).to_dict()
        
        return results
    
    def _run_parallel(
        self,
        tests: List[str],
        args_dict: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """Run tests in parallel using a thread pool."""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tests using the new unittest execution logic

            def _execute_single_unittest(test_id, test_specific_args):
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromName(test_id) # test_id is the full unittest ID

                # Before running, inject test_specific_args onto the TestCase instance(s)
                for test_case_or_suite in suite:
                    if hasattr(test_case_or_suite, '__iter__'): # It's a TestSuite
                        for test_case in test_case_or_suite:
                             if isinstance(test_case, unittest.TestCase):
                                setattr(test_case, 'config_args', test_specific_args)
                    elif isinstance(test_case_or_suite, unittest.TestCase): # It's a single TestCase
                        setattr(test_case_or_suite, 'config_args', test_specific_args)

                internal_runner = unittest.TextTestRunner(stream=None, verbosity=0)
                test_suite_result = internal_runner.run(suite)

                if test_suite_result.wasSuccessful():
                    status = TestStatus.PASS
                    notes = "Test passed."
                elif test_suite_result.errors:
                    status = TestStatus.ERROR
                    notes = "\n".join([f"{err[0].id()}: {err[1]}" for err in test_suite_result.errors])
                elif test_suite_result.failures:
                    status = TestStatus.FAIL
                    notes = "\n".join([f"{fail[0].id()}: {fail[1]}" for fail in test_suite_result.failures])
                else:
                    status = TestStatus.UNKNOWN
                    notes = "Test finished with an unknown status."

                return TestResult(test_name=test_id, status=status, notes=notes).to_dict()

            future_to_test = {
                executor.submit(_execute_single_unittest, test_name, args_dict.get(test_name, {})): test_name
                for test_name in tests
            }
            
            # Process results as they complete
            for i, future in enumerate(future_to_test): # Changed from future_to_test.items() to just future_to_test
                test_name = future_to_test[future]
                try:
                    result_dict = future.result()
                    results[test_name] = result_dict
                    
                    status_val = result_dict.get("test_details", {}).get("status", TestStatus.ERROR.value)
                    self.log(f"Test {test_name} completed with status: {status_val}")
                except Exception as e:
                    self.log(f"Error running test {test_name} in parallel: {str(e)}", "error")
                    results[test_name] = TestResult(
                        test_name=test_name, # test_name is the unittest id
                        status=TestStatus.ERROR,
                        notes=f"Exception during test execution: {str(e)}"
                    ).to_dict()
        
        return results
    
    def _log_summary(self, total_time: float) -> None:
        """Log a summary of test results."""
        # Count results by status
        status_counts = {status.value: 0 for status in TestStatus}
        for result in self.results.values():
            status = result.get("test_details", {}).get("status", "error")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Log summary
        self.log(f"Test run completed in {total_time:.2f} seconds")
        self.log(f"Total tests: {len(self.results)}")
        for status, count in status_counts.items():
            if count > 0:
                self.log(f"  {status}: {count}")
    
    def _save_report(self) -> None:
        """Save test results to a JSON file."""
        if not self.report_dir:
            return
            
        # Create a timestamp for the filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.report_dir, f"test_report_{timestamp}.json")
        
        # Save the results
        with open(filename, "w") as f:
            json.dump({
                "timestamp": timestamp,
                "results": self.results
            }, f, indent=2)
            
        self.log(f"Test report saved to {filename}")


def main():
    """Command-line interface for the test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Arcoa Nexus Test Runner")
    parser.add_argument(
        "--tests",
        nargs="*",
        help="Specific tests to run (if not specified, runs all tests)"
    )
    parser.add_argument(
        "--categories",
        nargs="*",
        choices=[c.value for c in TestCategory],
        help="Test categories to run"
    )
    parser.add_argument(
        "--severities",
        nargs="*",
        choices=[s.value for s in TestSeverity],
        help="Test severities to run"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Maximum number of parallel workers"
    )
    parser.add_argument(
        "--report-dir",
        help="Directory to save test reports"
    )
    parser.add_argument(
        "--list",
        action="store_true",
        help="List available tests and exit"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # List tests if requested
    if args.list:
        loader = unittest.TestLoader()
        # Assuming tests relevant to direct test_runner.py invocation are also in agent/tests
        suite = loader.discover(start_dir='agent/tests', pattern='test_*.py')

        print("Available tests (from unittest discovery via test_runner.py):")
        print("-" * 80)

        tests_with_metadata = []
        def extract_tests_from_suite(current_suite):
            if hasattr(current_suite, '__iter__'):
                for item in current_suite:
                    if isinstance(item, unittest.TestSuite):
                        extract_tests_from_suite(item)
                    elif isinstance(item, unittest.TestCase):
                        test_method_names = [m for m in dir(item) if m.startswith(loader.testMethodPrefix)]
                        for name in test_method_names:
                            full_test_id = f"{item.__module__}.{item.__class__.__name__}.{name}"
                            metadata = get_test_metadata_from_test_case(item, name)
                            tests_with_metadata.append({
                                "id": full_test_id, "method_name": name,
                                "class_name": item.__class__.__name__, "module_name": item.__module__,
                                "metadata": metadata
                            })
        extract_tests_from_suite(suite)

        if not tests_with_metadata:
            print("  No tests found matching 'test_*.py' in 'agent/tests/'")
        else:
            sorted_tests = sorted(tests_with_metadata, key=lambda x: (x["module_name"], x["class_name"], x["method_name"]))
            for test_info in sorted_tests:
                print(f"  ID: {test_info['id']}")
                if test_info["metadata"]:
                    meta = test_info["metadata"]
                    cat = meta.get('category', 'N/A')
                    sev = meta.get('severity', 'N/A')
                    desc = meta.get('description', 'No description')
                    display_name = meta.get('name', test_info['method_name'])
                    print(f"    Name: {display_name}")
                    print(f"    Category: {cat.value if hasattr(cat, 'value') else cat}")
                    print(f"    Severity: {sev.value if hasattr(sev, 'value') else sev}")
                    print(f"    Description: {desc}")
                else:
                    print(f"    Name: {test_info['method_name']}")
                    print(f"    (No category/severity metadata found)")
                print()
        return
    
    # Create test runner
    runner = TestRunner(
        report_dir=args.report_dir,
        parallel=args.parallel,
        max_workers=args.workers
    )
    
    # --- Test Discovery and Filtering for direct execution ---
    loader = unittest.TestLoader()
    suite = loader.discover(start_dir='agent/tests', pattern='test_*.py')

    filter_categories_args = set(TestCategory(c) for c in args.categories) if args.categories else None
    filter_severities_args = set(TestSeverity(s) for s in args.severities) if args.severities else None

    candidate_test_ids: List[str] = []
    def process_suite_for_filtering(current_suite):
        if hasattr(current_suite, '__iter__'):
            for item in current_suite:
                if isinstance(item, unittest.TestSuite):
                    process_suite_for_filtering(item)
                elif isinstance(item, unittest.TestCase):
                    test_method_names = [m for m in dir(item) if m.startswith(loader.testMethodPrefix)]
                    for name in test_method_names:
                        test_id = f"{item.__module__}.{item.__class__.__name__}.{name}"
                        metadata = get_test_metadata_from_test_case(item, name)
                        if metadata:
                            if filter_categories_args:
                                meta_category = metadata.get('category')
                                if not isinstance(meta_category, TestCategory): meta_category = TestCategory(meta_category)
                                if meta_category not in filter_categories_args: continue
                            if filter_severities_args:
                                meta_severity = metadata.get('severity')
                                if not isinstance(meta_severity, TestSeverity): meta_severity = TestSeverity(meta_severity)
                                if meta_severity not in filter_severities_args: continue
                        elif filter_categories_args or filter_severities_args:
                            continue # Skip if filters active and no metadata
                        candidate_test_ids.append(test_id)
    process_suite_for_filtering(suite)
    
    unique_candidate_test_ids = sorted(list(set(candidate_test_ids)))

    tests_to_run_final: List[str]
    if args.tests: # User specified specific tests
        tests_to_run_final = [tid for tid in unique_candidate_test_ids if tid in args.tests]
        if not tests_to_run_final and args.tests:
            logger.warning(f"Specified tests ({', '.join(args.tests)}) not found after category/severity filters or do not exist.")
            return 1 # Indicate error or no tests run
    else:
        tests_to_run_final = unique_candidate_test_ids

    if not tests_to_run_final:
        logger.warning("No tests selected to run after filtering.")
        return 0

    logger.info(f"Direct execution via test_runner.py: Running {len(tests_to_run_final)} tests.")
    if filter_categories_args: logger.info(f"Applied category filters: {', '.join(c.value for c in filter_categories_args)}")
    if filter_severities_args: logger.info(f"Applied severity filters: {', '.join(s.value for s in filter_severities_args)}")


    # Prepare test_args (minimal for direct runner, primarily log_callback)
    test_run_args = {test_id: {"log_callback": runner.log_callback} for test_id in tests_to_run_final}

    # Run tests using the runner instance
    runner.run_tests(
        tests=tests_to_run_final,
        # categories and severities are no longer passed to run_tests method
        test_args=test_run_args
    )


if __name__ == "__main__":
    main()

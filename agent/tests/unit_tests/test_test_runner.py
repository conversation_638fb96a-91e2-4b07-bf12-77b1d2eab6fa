#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus Test Runner

This module contains unit tests for the test runner.
"""
import os
import sys
import tempfile
import unittest
from unittest.mock import MagicMock, patch

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.test_framework import TestCategory, TestSeverity, TestStatus
from agent.tests.test_runner import TestRunner


class TestRunnerTests(unittest.TestCase):
    """Tests for the test runner."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.log_callback = MagicMock()
        self.temp_dir = tempfile.TemporaryDirectory()
        self.report_dir = self.temp_dir.name
    
    def tearDown(self):
        """Clean up test fixtures."""
        self.temp_dir.cleanup()
    
    def test_runner_initialization(self):
        """Test initializing the TestRunner."""
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir,
            parallel=True,
            max_workers=2
        )
        
        # Check attributes
        self.assertEqual(runner.log_callback, self.log_callback)
        self.assertEqual(runner.report_dir, self.report_dir)
        self.assertTrue(runner.parallel)
        self.assertEqual(runner.max_workers, 2)
        self.assertEqual(runner.results, {})
    
    @patch("agent.tests.test_runner.run_test_by_name")
    def test_run_tests_sequential(self, mock_run_test):
        """Test running tests sequentially."""
        # Mock test results
        mock_run_test.side_effect = [
            {"test_details": {"status": "pass", "notes": "Test 1 passed"}},
            {"test_details": {"status": "fail", "notes": "Test 2 failed"}}
        ]
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir,
            parallel=False
        )
        
        # Run tests
        tests = ["test1", "test2"]
        results = runner.run_tests(tests)
        
        # Check that the correct tests were run
        self.assertEqual(mock_run_test.call_count, 2)
        mock_run_test.assert_any_call("test1", log_callback=self.log_callback)
        mock_run_test.assert_any_call("test2", log_callback=self.log_callback)
        
        # Check results
        self.assertEqual(len(results), 2)
        self.assertEqual(results["test1"]["test_details"]["status"], "pass")
        self.assertEqual(results["test2"]["test_details"]["status"], "fail")
    
    @patch("agent.tests.test_runner.run_test_by_name")
    def test_run_tests_parallel(self, mock_run_test):
        """Test running tests in parallel."""
        # Mock test results
        mock_run_test.side_effect = [
            {"test_details": {"status": "pass", "notes": "Test 1 passed"}},
            {"test_details": {"status": "fail", "notes": "Test 2 failed"}}
        ]
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir,
            parallel=True,
            max_workers=2
        )
        
        # Run tests
        tests = ["test1", "test2"]
        results = runner.run_tests(tests)
        
        # Check that the correct tests were run
        self.assertEqual(mock_run_test.call_count, 2)
        mock_run_test.assert_any_call("test1", log_callback=self.log_callback)
        mock_run_test.assert_any_call("test2", log_callback=self.log_callback)
        
        # Check results
        self.assertEqual(len(results), 2)
        self.assertIn("test1", results)
        self.assertIn("test2", results)
    
    @patch("agent.tests.test_runner.run_test_by_name")
    def test_run_tests_with_args(self, mock_run_test):
        """Test running tests with custom arguments."""
        # Mock test results
        mock_run_test.return_value = {"test_details": {"status": "pass"}}
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir
        )
        
        # Run tests with custom arguments
        tests = ["test1"]
        test_args = {"test1": {"arg1": "value1", "arg2": 42}}
        runner.run_tests(tests, test_args=test_args)
        
        # Check that the test was run with the correct arguments
        mock_run_test.assert_called_once_with(
            "test1",
            log_callback=self.log_callback,
            arg1="value1",
            arg2=42
        )
    
    @patch("agent.tests.test_runner.run_test_by_name")
    @patch("agent.tests.test_runner.get_available_tests")
    def test_run_tests_with_category_filter(self, mock_get_tests, mock_run_test):
        """Test running tests filtered by category."""
        # Mock available tests
        mock_get_tests.return_value = [
            {
                "full_path": "test1",
                "category": TestCategory.CPU.value,
                "severity": TestSeverity.HIGH.value
            },
            {
                "full_path": "test2",
                "category": TestCategory.MEMORY.value,
                "severity": TestSeverity.MEDIUM.value
            }
        ]
        
        # Mock test results
        mock_run_test.return_value = {"test_details": {"status": "pass"}}
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir
        )
        
        # Run tests filtered by category
        categories = {TestCategory.CPU}
        runner.run_tests(["test1", "test2"], categories=categories)
        
        # Check that only the CPU test was run
        mock_run_test.assert_called_once_with("test1", log_callback=self.log_callback)
    
    @patch("agent.tests.test_runner.run_test_by_name")
    @patch("agent.tests.test_runner.get_available_tests")
    def test_run_tests_with_severity_filter(self, mock_get_tests, mock_run_test):
        """Test running tests filtered by severity."""
        # Mock available tests
        mock_get_tests.return_value = [
            {
                "full_path": "test1",
                "category": TestCategory.CPU.value,
                "severity": TestSeverity.HIGH.value
            },
            {
                "full_path": "test2",
                "category": TestCategory.MEMORY.value,
                "severity": TestSeverity.MEDIUM.value
            }
        ]
        
        # Mock test results
        mock_run_test.return_value = {"test_details": {"status": "pass"}}
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir
        )
        
        # Run tests filtered by severity
        severities = {TestSeverity.HIGH}
        runner.run_tests(["test1", "test2"], severities=severities)
        
        # Check that only the high severity test was run
        mock_run_test.assert_called_once_with("test1", log_callback=self.log_callback)
    
    @patch("agent.tests.test_runner.run_test_by_name")
    def test_run_tests_with_exception(self, mock_run_test):
        """Test handling exceptions during test execution."""
        # Mock test function to raise an exception
        mock_run_test.side_effect = Exception("Test error")
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir
        )
        
        # Run tests
        tests = ["test1"]
        results = runner.run_tests(tests)
        
        # Check that the exception was handled
        self.assertEqual(len(results), 1)
        self.assertEqual(results["test1"]["test_details"]["status"], "error")
        self.assertIn("Test error", results["test1"]["test_details"]["notes"])
    
    @patch("agent.tests.test_runner.run_test_by_name")
    def test_save_report(self, mock_run_test):
        """Test saving a test report."""
        # Mock test results
        mock_run_test.return_value = {"test_details": {"status": "pass"}}
        
        # Create a test runner
        runner = TestRunner(
            log_callback=self.log_callback,
            report_dir=self.report_dir
        )
        
        # Run tests
        tests = ["test1"]
        runner.run_tests(tests)
        
        # Check that a report file was created
        report_files = os.listdir(self.report_dir)
        self.assertEqual(len(report_files), 1)
        self.assertTrue(report_files[0].startswith("test_report_"))
        self.assertTrue(report_files[0].endswith(".json"))


if __name__ == "__main__":
    unittest.main()

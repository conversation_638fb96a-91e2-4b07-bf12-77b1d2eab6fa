#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus Test Framework

This module contains unit tests for the test framework itself.
"""
import datetime
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.test_framework import (
    TestCategory, TestResult, TestSeverity, TestStatus, test, get_available_tests
)


class TestFrameworkTests(unittest.TestCase):
    """Tests for the test framework."""

    def test_test_result_creation(self):
        """Test creating a TestResult instance."""
        # Create a test result
        result = TestResult(
            test_name="test_example",
            status=TestStatus.PASS,
            notes="Test passed successfully",
            details={"value": 42}
        )

        # Check attributes
        self.assertEqual(result.test_name, "test_example")
        self.assertEqual(result.status, TestStatus.PASS)
        self.assertEqual(result.notes, "Test passed successfully")
        self.assertEqual(result.details, {"value": 42})

        # Check that timestamps were created
        self.assertIsInstance(result.started_at, datetime.datetime)
        self.assertIsInstance(result.finished_at, datetime.datetime)

    def test_test_result_to_dict(self):
        """Test converting a TestResult to a dictionary."""
        # Create a test result with specific timestamps
        start_time = datetime.datetime(2023, 1, 1, 12, 0, 0)
        end_time = datetime.datetime(2023, 1, 1, 12, 0, 10)

        result = TestResult(
            test_name="test_example",
            status=TestStatus.PASS,
            notes="Test passed successfully",
            details={"value": 42},
            started_at=start_time,
            finished_at=end_time
        )

        # Convert to dictionary
        result_dict = result.to_dict()

        # Check dictionary structure
        self.assertIn("test_details", result_dict)
        self.assertIn("started_at", result_dict)
        self.assertIn("finished_at", result_dict)

        # Check test details
        test_details = result_dict["test_details"]
        self.assertEqual(test_details["status"], "pass")
        self.assertEqual(test_details["notes"], "Test passed successfully")
        self.assertEqual(test_details["value"], 42)

        # Check timestamps
        self.assertEqual(result_dict["started_at"], start_time.isoformat())
        self.assertEqual(result_dict["finished_at"], end_time.isoformat())

    def test_test_result_from_dict(self):
        """Test creating a TestResult from a dictionary."""
        # Create a dictionary representation
        result_dict = {
            "test_details": {
                "status": "pass",
                "notes": "Test passed successfully",
                "value": 42
            },
            "started_at": "2023-01-01T12:00:00",
            "finished_at": "2023-01-01T12:00:10"
        }

        # Create TestResult from dictionary
        result = TestResult.from_dict(result_dict, "test_example")

        # Check attributes
        self.assertEqual(result.test_name, "test_example")
        self.assertEqual(result.status, TestStatus.PASS)
        self.assertEqual(result.notes, "Test passed successfully")
        self.assertEqual(result.details, {"value": 42})

        # Check timestamps
        self.assertEqual(result.started_at, datetime.datetime(2023, 1, 1, 12, 0, 0))
        self.assertEqual(result.finished_at, datetime.datetime(2023, 1, 1, 12, 0, 10))

    def test_test_decorator(self):
        """Test the @test decorator."""
        # Define a test function with the decorator
        @test(
            category=TestCategory.CPU,
            severity=TestSeverity.HIGH,
            description="Test function"
        )
        def example_test():
            return True

        # Check that metadata was added to the function
        self.assertTrue(hasattr(example_test, "test_metadata"))

        # Check metadata values
        metadata = example_test.test_metadata
        self.assertEqual(metadata["category"], TestCategory.CPU)
        self.assertEqual(metadata["severity"], TestSeverity.HIGH)
        self.assertEqual(metadata["description"], "Test function")
        self.assertEqual(metadata["name"], "example_test")

    def test_test_decorator_with_custom_name(self):
        """Test the @test decorator with a custom name parameter."""
        # Define a test function with the decorator and custom name
        @test(
            category=TestCategory.CPU,
            severity=TestSeverity.HIGH,
            description="Test function",
            name="Custom Test Name"
        )
        def example_test():
            return True

        # Check that metadata was added to the function
        self.assertTrue(hasattr(example_test, "test_metadata"))

        # Check metadata values
        metadata = example_test.test_metadata
        self.assertEqual(metadata["category"], TestCategory.CPU)
        self.assertEqual(metadata["severity"], TestSeverity.HIGH)
        self.assertEqual(metadata["description"], "Test function")
        self.assertEqual(metadata["name"], "Custom Test Name")

    def test_test_decorator_execution(self):
        """Test that the @test decorator properly wraps function execution."""
        # Define a test function with the decorator
        @test(
            category=TestCategory.CPU,
            severity=TestSeverity.HIGH
        )
        def example_test(value):
            return value

        # Call the decorated function
        result = example_test(True)

        # Check that the result is properly formatted
        self.assertIn("test_details", result)
        self.assertIn("metadata", result)

        # Check test details
        test_details = result["test_details"]
        self.assertEqual(test_details["status"], "pass")

        # Check metadata
        metadata = result["metadata"]
        self.assertEqual(metadata["category"], TestCategory.CPU)
        self.assertEqual(metadata["severity"], TestSeverity.HIGH)

    def test_test_decorator_with_exception(self):
        """Test that the @test decorator handles exceptions properly."""
        # Define a test function that raises an exception
        @test(
            category=TestCategory.CPU,
            severity=TestSeverity.HIGH
        )
        def failing_test():
            raise ValueError("Test error")

        # Call the decorated function
        result = failing_test()

        # Check that the result indicates an error
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        self.assertEqual(test_details["status"], "error")
        self.assertIn("Test error", test_details["notes"])

    @patch("agent.tests.test_framework.pkgutil")
    @patch("agent.tests.test_framework.importlib")
    @patch("agent.tests.test_framework.inspect")
    def test_get_available_tests(self, mock_inspect, mock_importlib, mock_pkgutil):
        """Test the get_available_tests function."""
        # Set up the directory path
        import os
        from agent.tests.test_framework import get_available_tests

        # Mock pkgutil.iter_modules to return some test modules
        mock_pkgutil.iter_modules.return_value = [
            (None, "cpu_test", None),
            (None, "ram_test", None),
            (None, "test_framework", None)  # This one should be skipped
        ]

        # Mock importlib.import_module to return mock modules
        cpu_module = MagicMock()
        ram_module = MagicMock()
        mock_importlib.import_module.side_effect = lambda name: {
            "agent.tests.cpu_test": cpu_module,
            "agent.tests.ram_test": ram_module
        }[name]

        # Mock inspect.getmembers to return test functions
        cpu_test_func = MagicMock()
        cpu_test_func.test_metadata = {
            "category": "cpu",
            "severity": "high",
            "description": "CPU test",
            "name": "run_cpu_test"
        }

        ram_test_func = MagicMock()
        ram_test_func.test_metadata = {
            "category": "memory",
            "severity": "medium",
            "description": "RAM test",
            "name": "run_ram_test"
        }

        # Set up the inspect mocks
        mock_inspect.isfunction.side_effect = lambda obj: obj in [cpu_test_func, ram_test_func]
        mock_inspect.getmembers.side_effect = [
            [("run_cpu_test", cpu_test_func)],
            [("run_ram_test", ram_test_func)]
        ]

        # Call get_available_tests
        tests = get_available_tests()

        # Check that the correct tests were found
        self.assertEqual(len(tests), 2)

        # Check the first test
        self.assertEqual(tests[0]["name"], "run_cpu_test")
        self.assertEqual(tests[0]["category"], "cpu")
        self.assertEqual(tests[0]["severity"], "high")
        self.assertEqual(tests[0]["description"], "CPU test")
        self.assertEqual(tests[0]["module"], "cpu_test")
        self.assertEqual(tests[0]["full_path"], "agent.tests.cpu_test.run_cpu_test")

        # Check the second test
        self.assertEqual(tests[1]["name"], "run_ram_test")
        self.assertEqual(tests[1]["category"], "memory")
        self.assertEqual(tests[1]["severity"], "medium")
        self.assertEqual(tests[1]["description"], "RAM test")
        self.assertEqual(tests[1]["module"], "ram_test")
        self.assertEqual(tests[1]["full_path"], "agent.tests.ram_test.run_ram_test")


if __name__ == "__main__":
    unittest.main()

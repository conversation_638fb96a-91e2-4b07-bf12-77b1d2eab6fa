#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus Test Configuration

This module contains unit tests for the test configuration.
"""
import os
import sys
import unittest
from unittest.mock import patch

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.test_config import (
    DEFAULT_SETTINGS, get_setting, get_test_suite, get_test_args
)


class TestConfigTests(unittest.TestCase):
    """Tests for the test configuration."""
    
    def test_default_settings(self):
        """Test that default settings are defined."""
        # Check that default settings exist
        self.assertIsInstance(DEFAULT_SETTINGS, dict)
        self.assertGreater(len(DEFAULT_SETTINGS), 0)
        
        # Check some specific settings
        self.assertIn("report_dir", DEFAULT_SETTINGS)
        self.assertIn("log_level", DEFAULT_SETTINGS)
        self.assertIn("cpu_stress_duration", DEFAULT_SETTINGS)
        self.assertIn("ram_test_size_mb", DEFAULT_SETTINGS)
    
    def test_get_setting_default(self):
        """Test getting a setting with default value."""
        # Get a setting that exists in DEFAULT_SETTINGS
        value = get_setting("report_dir")
        self.assertEqual(value, DEFAULT_SETTINGS["report_dir"])
        
        # Get a setting that doesn't exist
        value = get_setting("nonexistent_setting", "default_value")
        self.assertEqual(value, "default_value")
    
    @patch.dict(os.environ, {"ARCOA_REPORT_DIR": "/custom/path"})
    def test_get_setting_from_env(self):
        """Test getting a setting from environment variables."""
        # Get a setting from environment variables
        value = get_setting("report_dir")
        self.assertEqual(value, "/custom/path")
    
    @patch.dict(os.environ, {"ARCOA_CPU_STRESS_DURATION": "20"})
    def test_get_setting_type_conversion_int(self):
        """Test type conversion for integer settings."""
        # Get an integer setting from environment variables
        value = get_setting("cpu_stress_duration")
        self.assertEqual(value, 20)
        self.assertIsInstance(value, int)
    
    @patch.dict(os.environ, {"ARCOA_CPU_MIN_LOAD": "75.5"})
    def test_get_setting_type_conversion_float(self):
        """Test type conversion for float settings."""
        # Get a float setting from environment variables
        value = get_setting("cpu_min_load")
        self.assertEqual(value, 75.5)
        self.assertIsInstance(value, float)
    
    @patch.dict(os.environ, {"ARCOA_ENABLE_REAL_WIPE": "true"})
    def test_get_setting_type_conversion_bool(self):
        """Test type conversion for boolean settings."""
        # Get a boolean setting from environment variables
        value = get_setting("enable_real_wipe")
        self.assertTrue(value)
        self.assertIsInstance(value, bool)
    
    def test_get_test_suite(self):
        """Test getting predefined test suites."""
        # Get the default suite
        default_suite = get_test_suite()
        self.assertIsInstance(default_suite, list)
        self.assertGreater(len(default_suite), 0)
        
        # Get a specific suite
        full_suite = get_test_suite("full")
        self.assertIsInstance(full_suite, list)
        self.assertGreater(len(full_suite), 0)
        
        # Check that the full suite has more tests than the default suite
        self.assertGreater(len(full_suite), len(default_suite))
        
        # Get a nonexistent suite (should return default)
        nonexistent_suite = get_test_suite("nonexistent")
        self.assertEqual(nonexistent_suite, default_suite)
    
    def test_get_test_args(self):
        """Test getting default arguments for tests."""
        # Get arguments for a CPU stress test
        cpu_args = get_test_args("agent.tests.cpu_test.run_cpu_stress_test")
        self.assertIsInstance(cpu_args, dict)
        self.assertIn("duration_seconds", cpu_args)
        self.assertEqual(cpu_args["duration_seconds"], DEFAULT_SETTINGS["cpu_stress_duration"])
        
        # Get arguments for a RAM test
        ram_args = get_test_args("agent.tests.ram_test.run_ram_test")
        self.assertIsInstance(ram_args, dict)
        self.assertIn("test_size_mb", ram_args)
        self.assertEqual(ram_args["test_size_mb"], DEFAULT_SETTINGS["ram_test_size_mb"])
        
        # Get arguments for a nonexistent test
        nonexistent_args = get_test_args("nonexistent_test")
        self.assertEqual(nonexistent_args, {})


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python3
"""
Unit Tests for the Arcoa Nexus CPU Test Module

This module contains unit tests for the CPU test module.
"""
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

# Skip importing this deprecated unit-test module; legacy CPU tests removed.
raise unittest.SkipTest("Deprecated CPU unit-test module; functional tests moved or removed.")

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.cpu_test import run_basic_cpu_test, run_cpu_stress_test
from agent.tests.test_framework import TestStatus


class CPUTestTests(unittest.TestCase):
    """Tests for the CPU test module."""
    
    @patch("agent.tests.cpu_test.psutil")
    @patch("agent.tests.cpu_test.platform")
    def test_run_basic_cpu_test(self, mock_platform, mock_psutil):
        """Test the basic CPU test."""
        # Mock platform.system
        mock_platform.system.return_value = "Linux"
        
        # Mock psutil.cpu_percent
        mock_psutil.cpu_percent.side_effect = [50.0, [45.0, 55.0]]
        
        # Mock psutil.cpu_freq
        mock_freq = MagicMock()
        mock_freq.current = 2500.0
        mock_psutil.cpu_freq.return_value = mock_freq
        
        # Mock psutil.cpu_count
        mock_psutil.cpu_count.side_effect = [4, 8]  # physical, logical
        
        # Mock open for reading /proc/cpuinfo
        mock_open = unittest.mock.mock_open(read_data="model name : Intel Core i7-1234U\n")
        with patch("builtins.open", mock_open):
            # Run the test
            result = run_basic_cpu_test()
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.PASS.value)
        
        # Check CPU metrics
        self.assertEqual(test_details["cpu_percent"], 50.0)
        self.assertEqual(test_details["per_core_percent"], [45.0, 55.0])
        self.assertEqual(test_details["current_freq_mhz"], 2500.0)
        
        # Check CPU info
        self.assertIn("cpu_info", test_details)
        cpu_info = test_details["cpu_info"]
        self.assertEqual(cpu_info["cores"], 4)
        self.assertEqual(cpu_info["threads"], 8)
        self.assertIn("model", cpu_info)
    
    @patch("agent.tests.cpu_test.psutil")
    @patch("agent.tests.cpu_test.platform")
    @patch("agent.tests.cpu_test.multiprocessing")
    @patch("agent.tests.cpu_test.time")
    def test_run_cpu_stress_test(self, mock_time, mock_multiprocessing, mock_platform, mock_psutil):
        """Test the CPU stress test."""
        # Mock platform.system
        mock_platform.system.return_value = "Linux"
        
        # Mock time.time to control the test duration
        mock_time.time.side_effect = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        
        # Mock psutil.cpu_percent for monitoring
        mock_psutil.cpu_percent.return_value = 85.0
        
        # Mock psutil.cpu_count
        mock_psutil.cpu_count.return_value = 4
        
        # Mock multiprocessing.Process
        mock_process = MagicMock()
        mock_multiprocessing.Process.return_value = mock_process
        
        # Mock psutil.sensors_temperatures for temperature monitoring
        mock_temps = {
            "coretemp": [
                MagicMock(label="Core 0", current=45.0),
                MagicMock(label="Core 1", current=46.0)
            ]
        }
        mock_psutil.sensors_temperatures.side_effect = [mock_temps, {
            "coretemp": [
                MagicMock(label="Core 0", current=50.0),
                MagicMock(label="Core 1", current=52.0)
            ]
        }]
        
        # Run the test
        result = run_cpu_stress_test(duration_seconds=5)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status
        self.assertEqual(test_details["status"], TestStatus.PASS.value)
        
        # Check CPU metrics
        self.assertEqual(test_details["duration_seconds"], 5)
        self.assertGreaterEqual(test_details["avg_cpu_load"], 80.0)
        self.assertGreaterEqual(test_details["max_cpu_load"], 80.0)
        
        # Check temperature delta
        self.assertIn("temperature_delta", test_details)
        temp_delta = test_details["temperature_delta"]
        self.assertGreaterEqual(temp_delta.get("coretemp_Core 0", 0), 0)
        
        # Check that processes were created and managed
        self.assertEqual(mock_multiprocessing.Process.call_count, 4)  # One per core
        self.assertEqual(mock_process.start.call_count, 4)
        self.assertEqual(mock_process.join.call_count, 4)
    
    @patch("agent.tests.cpu_test.psutil")
    def test_run_cpu_stress_test_low_load(self, mock_psutil):
        """Test the CPU stress test with low CPU load (should fail)."""
        # Mock psutil.cpu_percent for monitoring (low load)
        mock_psutil.cpu_percent.return_value = 30.0
        
        # Mock psutil.cpu_count
        mock_psutil.cpu_count.return_value = 4
        
        # Mock time.time to control the test duration
        with patch("agent.tests.cpu_test.time.time") as mock_time:
            mock_time.side_effect = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
            
            # Run the test
            result = run_cpu_stress_test(duration_seconds=5)
        
        # Check that the result has the expected structure
        self.assertIn("test_details", result)
        test_details = result["test_details"]
        
        # Check status (should fail due to low load)
        self.assertEqual(test_details["status"], TestStatus.FAIL.value)
        
        # Check fail reason
        self.assertIn("fail_reason", test_details)
        self.assertIn("could not reach sufficient load", test_details["fail_reason"].lower())


if __name__ == "__main__":
    unittest.main()

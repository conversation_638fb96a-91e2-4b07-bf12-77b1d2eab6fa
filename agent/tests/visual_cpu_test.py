"""
Arcoa Nexus Visual CPU Stress Test (Tkinter)

A simple, user-driven visual CPU stress test for diagnostics. Shows real-time CPU load in a Tkinter window. User can cancel the test.
"""
import time
import threading
import psutil
import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable
from agent.gui.theme import COLORS
from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)
import multiprocessing

def cpu_stress_worker(stop_event):
    """Worker function that stresses CPU until stop event is set."""
    x = 1.0
    while not stop_event.is_set():
        x = (x * 1.000001 + 0.000001) % 1000000

class VisualCPUTestWindow(tk.Toplevel):
    def __init__(self, parent, timeout: int = 10, log_callback: Optional[Callable] = None):
        super().__init__(parent)
        self.timeout = timeout
        self.log_callback = log_callback
        self.result = None
        self._closed = False
        self._start_time = time.time()
        self.cpu_samples = []
        self.title("Visual CPU Stress Test")
        self.protocol("WM_DELETE_WINDOW", self._on_close)
        self.configure(bg=COLORS["bg_dark"])
        self.resizable(False, False)
        self._setup_ui()
        psutil.cpu_percent(interval=0.1)  # Initialize psutil
        self._stress_stop_event = None
        self._workers = []
        self._start_stress()
        self._update_cpu()
        self._update_timer()
        self.grab_set()
        self.focus_force()

    def _setup_ui(self):
        """Setup the user interface components."""
        bg = COLORS["bg_dark"]
        fg = COLORS["text_light"]
        style = ttk.Style(self)
        style.theme_use('clam')
        style.configure('TProgressbar', background=COLORS.get("primary", "#ad2b2b"), troughcolor=bg)
        style.configure('CPU.TButton', background=COLORS.get("button", "#313131"), foreground=fg)

        tk.Label(self, text="Visual CPU Stress Test", font=("Arial", 16, "bold"), bg=bg, fg=fg).pack(pady=(18, 6))
        tk.Label(self, text="CPU stress test in progress. Please wait...",
                 font=("Arial", 12), bg=bg, fg=fg).pack(pady=(0, 12))

        self.progress_var = tk.DoubleVar(value=0)
        self.progress = ttk.Progressbar(self, variable=self.progress_var, maximum=100, style='TProgressbar', length=320)
        self.progress.pack(padx=24, pady=8)

        self.cpu_label = tk.Label(self, text="CPU Usage: 0%", font=("Arial", 13), bg=bg, fg=fg)
        self.cpu_label.pack(pady=(4, 12))

        self.timer_label = tk.Label(self, text=f"Time left: {self.timeout}s", font=("Arial", 11), bg=bg, fg=fg)
        self.timer_label.pack(pady=(0, 10))

        btn_frame = tk.Frame(self, bg=bg)
        btn_frame.pack(pady=(6, 14))
        self.cancel_btn = ttk.Button(btn_frame, text="Cancel", command=self._on_cancel, style='CPU.TButton')
        self.cancel_btn.grid(row=0, column=0, padx=10)

    def _start_stress(self):
        """Start CPU stress workers on all available cores."""
        self._stress_stop_event = multiprocessing.Event()
        num_cores = multiprocessing.cpu_count()
        self._workers = []
        for _ in range(num_cores):
            p = multiprocessing.Process(target=cpu_stress_worker, args=(self._stress_stop_event,))
            p.daemon = True
            p.start()
            self._workers.append(p)

    def _stop_stress(self):
        """Stop all CPU stress workers."""
        if self._stress_stop_event:
            self._stress_stop_event.set()
        for p in self._workers:
            if p.is_alive():
                p.terminate()
                p.join(timeout=1.0)
        self._workers = []

    def _update_cpu(self):
        """Update CPU usage display and collect samples."""
        if self._closed:
            return
        usage = psutil.cpu_percent(interval=None)
        self.cpu_samples.append(usage)
        self.progress_var.set(usage)
        self.cpu_label.config(text=f"CPU Usage: {usage:.1f}%")
        self.after(700, self._update_cpu)

    def _update_timer(self):
        """Update countdown timer and finish test when time expires."""
        if self._closed:
            return
        elapsed = int(time.time() - self._start_time)
        left = max(0, self.timeout - elapsed)
        self.timer_label.config(text=f"Time left: {left}s")
        if left <= 0:
            self._finish_test()
        else:
            self.after(500, self._update_timer)

    def _finish_test(self):
        """Complete the test successfully after full duration."""
        self._stop_stress()
        
        avg_load = sum(self.cpu_samples) / len(self.cpu_samples) if self.cpu_samples else 0
        max_load = max(self.cpu_samples) if self.cpu_samples else 0
        
        # Test always passes if it completes the full duration
        notes = (f"CPU stress test completed for {self.timeout}s. "
                 f"Avg CPU load: {avg_load:.1f}%. Max CPU load: {max_load:.1f}%.")
        
        self.result = {
            "status": TestStatus.PASS,
            "notes": notes,
            "avg_cpu_load": avg_load,
            "max_cpu_load": max_load,
            "duration_seconds": self.timeout
        }
        self._closed = True
        self.grab_release()
        self.destroy()

    def _on_cancel(self):
        """Handle test cancellation by user."""
        elapsed = time.time() - self._start_time
        self._stop_stress()
        self._closed = True
        
        avg_load = sum(self.cpu_samples) / len(self.cpu_samples) if self.cpu_samples else 0
        max_load = max(self.cpu_samples) if self.cpu_samples else 0
        
        self.result = {
            "status": TestStatus.SKIPPED,
            "notes": f"Test cancelled by user after {elapsed:.1f}s.",
            "avg_cpu_load": avg_load,
            "max_cpu_load": max_load,
            "duration_seconds": elapsed
        }
        self.grab_release()
        self.destroy()

    def _on_close(self):
        """Handle window close event same as cancellation."""
        self._on_cancel()

@test(
    category=TestCategory.CPU,
    severity=TestSeverity.HIGH,
    description="Stresses all CPU cores for a specified duration (default 10s). PASSES if test completes without cancellation. Shows live CPU usage.",
    name="Visual CPU Stress Test",
    timeout=30
)
def visual_cpu_test(log_callback=None, parent_window=None, duration=10, **kwargs):
    """
    Show a window with a live CPU usage bar. Perform CPU stress test.
    Returns dict with status and notes.
    """
    if log_callback:
        log_callback("Launching Visual CPU Stress Test window...", "info")
    
    root = parent_window or tk.Tk()
    owns_root = parent_window is None
    result = None
    
    if owns_root:
        root.withdraw()
        win = VisualCPUTestWindow(root, timeout=duration, log_callback=log_callback)
        root.wait_window(win)
        result = win.result
        root.destroy()
    else:
        win = VisualCPUTestWindow(root, timeout=duration, log_callback=log_callback)
        root.wait_window(win)
        result = win.result

    if result is None:
        status = TestStatus.ERROR
        notes = "Test window closed unexpectedly"
        result_data = {
            "avg_cpu_load": 0,
            "max_cpu_load": 0,
            "duration_seconds": duration
        }
    else:
        status = result.get("status", TestStatus.ERROR)
        notes = result.get("notes", "No result notes available")
        result_data = {
            "avg_cpu_load": result.get("avg_cpu_load", 0),
            "max_cpu_load": result.get("max_cpu_load", 0),
            "duration_seconds": result.get("duration_seconds", duration)
        }

    if log_callback:
        log_callback(f"Visual CPU Stress Test completed: {status.name}", "info")
        
    return {"status": status, "notes": notes, **result_data}
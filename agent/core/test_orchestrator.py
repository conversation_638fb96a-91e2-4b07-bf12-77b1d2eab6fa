import tkinter as tk
from typing import Any, Callable, Dict, Optional
from agent.tests.display_test import run_lcd_test_gui
from agent.tests.keyboard_test import run_keyboard_test
from agent.tests.pointing_device_test import run_pointing_device_test
import platform
from agent.tests.visual_cpu_test import visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.web_visual_ram_test import run_web_visual_ram_test
from agent.tests.web_display_test import run_web_lcd_test_gui
from agent.tests.touch_screen_test import run_touch_screen_test
from agent.tests.profiles import Profile
from agent.core.result_manager import ResultManager
TYPE_A_TEST_PATHS_QUALIFIED = ['agent.tests.battery_test.run_battery_test', 'agent.tests.battery_test.run_battery_discharge_test', 'agent.tests.battery_test.run_battery_charge_test', 'agent.tests.battery_test.run_battery_full_assessment']
try:
    from agent.tests.cpu_test import run_basic_cpu_test, run_cpu_stress_test
except ImportError:
    from agent.tests.visual_cpu_test import visual_cpu_test as run_basic_cpu_test
    run_cpu_stress_test = None
try:
    from agent.tests.ram_test import run_ram_test, run_advanced_ram_test
except ImportError:
    from agent.tests.visual_ram_test import run_visual_ram_test as run_ram_test
    run_advanced_ram_test = None
if platform.system() == 'Linux':
    from agent.tests.drive_wipe_test import run_secure_wipe_test
else:
    run_secure_wipe_test = None
run_battery_test = run_battery_charge_test = run_battery_discharge_test = run_battery_full_assessment = None

class TestOrchestrator:

    def __init__(self, log_callback: Callable[[str, str], None], result_manager_instance: ResultManager, main_app_ref: tk.Tk, get_current_profile_callback: Callable[[], Optional[Profile]], get_asset_number_callback: Callable[[], str], get_operator_id_callback: Callable[[], str]):
        self.log_callback = log_callback
        self.result_manager_instance = result_manager_instance
        self.main_app_ref = main_app_ref
        self.get_current_profile_callback = get_current_profile_callback
        self.get_asset_number_callback = get_asset_number_callback
        self.get_operator_id_callback = get_operator_id_callback
        self.end_screen_summary_data: Dict[str, Any] = {}

    def _get_status_and_notes_from_result(self, result_data: Any) -> tuple[str, str]:
        """Helper function to extract status and notes from various result formats."""
        status = 'unknown'
        notes = ''
        if isinstance(result_data, dict):
            if 'test_details' in result_data:
                raw_status = result_data.get('test_details', {}).get('status', 'unknown')
                status = str(raw_status).lower() if raw_status is not None else 'unknown'
                notes = result_data.get('test_details', {}).get('notes', '')
            elif 'status' in result_data:
                raw_status = result_data.get('status', 'unknown')
                status = str(raw_status).lower() if raw_status is not None else 'unknown'
                raw_notes = result_data.get('notes', '')
                if isinstance(raw_notes, list):
                    notes = '; '.join(raw_notes)
                else:
                    notes = str(raw_notes)
            else:
                notes = str(result_data)
                if len(notes) > 200:
                    notes = notes[:200] + '...'
        elif isinstance(result_data, str):
            status = result_data.lower()
        else:
            status = 'error'
            notes = f'Unexpected result format: {type(result_data)}'
        return (status, notes)

    def execute_tests(self, headless_mode=False):
        current_profile = self.get_current_profile_callback()
        if not current_profile or not current_profile.tests:
            self.log_callback('No profile loaded or profile has no tests.', 'warning')
            self.end_screen_summary_data = {'overall_status': 'ERROR', 'test_results': [], 'drive_wipe_results': [], 'errors': ['No profile loaded or profile has no tests.']}
            return
        self.log_callback(f'Starting tests for profile: {current_profile.name}...', 'info')
        self.end_screen_summary_data = {'overall_status': 'PASS', 'test_results': [], 'drive_wipe_results': [], 'errors': []}
        tests_to_run = current_profile.tests
        # Choose appropriate visual tests based on mode
        visual_ram_test_func = run_web_visual_ram_test if headless_mode else run_visual_ram_test
        visual_ram_test_kwargs = {'log_callback': self.log_callback} if headless_mode else {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}

        lcd_test_func = run_web_lcd_test_gui if headless_mode else run_lcd_test_gui
        lcd_test_kwargs = {'log_callback': self.log_callback} if headless_mode else {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}

        test_function_map = {
            'agent.tests.cpu_test.run_basic_cpu_test': (run_basic_cpu_test, {'log_callback': self.log_callback}),
            'agent.tests.ram_test.run_ram_test': (run_ram_test, {'log_callback': self.log_callback}),
            'agent.tests.ram_test.run_advanced_ram_test': (run_advanced_ram_test, {'log_callback': self.log_callback}),
            'agent.tests.display_test.run_lcd_test_gui': (lcd_test_func, lcd_test_kwargs),
            'agent.tests.keyboard_test.run_keyboard_test': (run_keyboard_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.pointing_device_test.run_pointing_device_test': (run_pointing_device_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.visual_cpu_test.visual_cpu_test': (visual_cpu_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.visual_ram_test.run_visual_ram_test': (visual_ram_test_func, visual_ram_test_kwargs),
            'agent.tests.drive_wipe_test.run_secure_wipe_test': (run_secure_wipe_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.touch_screen_test.run_touch_screen_test': (run_touch_screen_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            **({'agent.tests.cpu_test.run_cpu_stress_test': (run_cpu_stress_test, {'log_callback': self.log_callback})} if run_cpu_stress_test else {})
        }
        for test_path_in_profile in tests_to_run:
            test_name_simple = test_path_in_profile.split('.')[-1].replace('run_', '').replace('_test', ' Test').title()
            if headless_mode and test_path_in_profile not in TYPE_A_TEST_PATHS_QUALIFIED:
                skip_msg = 'Skipped in headless mode'
                self.log_callback(f'Skipping GUI test {test_name_simple} in headless mode.', 'warning')
                self.result_manager_instance.add_result(
                    self.get_asset_number_callback(), 
                    self.get_operator_id_callback(), 
                    test_name_simple, 
                    {'test_details': {'status': 'skipped', 'notes': skip_msg}}
                )
                self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED', 'notes': skip_msg})
                continue
            if test_path_in_profile not in test_function_map:
                if test_path_in_profile.startswith('agent.tests.battery_test'):
                    skip_msg = f"Deprecated battery test '{test_name_simple}' skipped; battery info collected automatically."
                    self.log_callback(skip_msg, 'info')
                    self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'skipped', 'notes': skip_msg}})
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED'})
                    continue
                error_msg = f"Test function for '{test_path_in_profile}' not found in orchestrator map."
                self.log_callback(error_msg, 'error')
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'fail', 'notes': error_msg}})
                self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'FAIL'})
                self.end_screen_summary_data['errors'].append(error_msg)
                self.end_screen_summary_data['overall_status'] = 'FAIL'
                continue
            test_func, test_kwargs = test_function_map[test_path_in_profile]
            if test_func is None:
                skip_msg = f"Test '{test_name_simple}' ({test_path_in_profile}) is not available on this platform ({platform.system()}) and will be skipped."
                self.log_callback(skip_msg, 'warning')
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'skipped', 'notes': skip_msg}})
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Platform Skip', 'status': 'SKIPPED', 'details': skip_msg})
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED', 'notes': skip_msg})
                continue
            self.log_callback(f'Running {test_name_simple}...', 'info')
            try:
                result = test_func(**test_kwargs)
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, result)
                status, notes = self._get_status_and_notes_from_result(result)
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    if isinstance(result, list):
                        for drive_res in result:
                            drive_path = drive_res.get('drive', 'Unknown Drive')
                            drive_status = str(drive_res.get('status', 'unknown')).lower()
                            drive_details = drive_res.get('details', '')
                            self.end_screen_summary_data['drive_wipe_results'].append({'drive': drive_path, 'status': drive_status.upper(), 'details': drive_details})
                            if drive_status not in ['success', 'pass']:
                                self.end_screen_summary_data['overall_status'] = 'FAIL'
                                if drive_details:
                                    self.end_screen_summary_data['errors'].append(f'Wipe Error ({drive_path}): {drive_details}')
                    elif isinstance(result, dict) and 'status' in result:
                        wipe_status_overall = str(result.get('status', 'unknown')).lower()
                        wipe_details_overall = result.get('notes', result.get('details', ''))
                        self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Overall Wipe Status', 'status': wipe_status_overall.upper(), 'details': wipe_details_overall})
                        if wipe_status_overall not in ['success', 'pass']:
                            self.end_screen_summary_data['overall_status'] = 'FAIL'
                            if wipe_details_overall:
                                self.end_screen_summary_data['errors'].append(f'{test_name_simple}: {wipe_details_overall}')
                    else:
                        self.log_callback(f'{test_name_simple} result in unexpected format: {result}', 'warning')
                        self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Unknown', 'status': 'FAIL', 'details': 'Result in unexpected format'})
                        self.end_screen_summary_data['overall_status'] = 'FAIL'
                        self.end_screen_summary_data['errors'].append(f'{test_name_simple}: Result in unexpected format.')
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': status.upper()})
                    if status not in ['pass', 'success']:
                        self.end_screen_summary_data['overall_status'] = 'FAIL'
                        if notes:
                            self.end_screen_summary_data['errors'].append(f'{test_name_simple} ({status.upper()}): {notes}')
            except Exception as e:
                error_msg = f'Error running {test_name_simple}: {str(e)}'
                self.log_callback(error_msg, 'error')
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'fail', 'notes': error_msg}})
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Execution Error', 'status': 'FAIL', 'details': error_msg})
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'FAIL'})
                self.end_screen_summary_data['errors'].append(error_msg)
                self.end_screen_summary_data['overall_status'] = 'FAIL'
        final_log_level = 'success' if self.end_screen_summary_data['overall_status'] == 'PASS' else 'error'
        self.log_callback(f"All tests completed. Overall Status: {self.end_screen_summary_data['overall_status']}", final_log_level)
        if self.end_screen_summary_data['errors']:
            self.log_callback(f"Encountered {len(self.end_screen_summary_data['errors'])} error(s)/issue(s) during testing:", 'warning')
            for err_entry in self.end_screen_summary_data['errors']:
                self.log_callback(f' - {err_entry}', 'warning')
        asset_number = self.get_asset_number_callback()
        operator_id = self.get_operator_id_callback()
        if not asset_number:
            self.log_callback('Asset number not available, skipping results consolidation.', 'warning')
            asset_number = 'unknown_asset'
        if not operator_id:
            self.log_callback("Operator ID not available, will be set to 'unknown' in consolidation.", 'info')
            operator_id = 'unknown_operator'
        if asset_number != 'unknown_asset':
            try:
                consolidated_file_path = self.result_manager_instance.consolidate_results_for_asset(asset_number, operator_id)
                if consolidated_file_path:
                    self.log_callback(f'Successfully consolidated results for asset {asset_number} to {consolidated_file_path}', 'success')
                else:
                    self.log_callback(f'Failed to consolidate results for asset {asset_number}. See previous logs for details.', 'warning')
            except Exception as e:
                self.log_callback(f'An unexpected error occurred during results consolidation for asset {asset_number}: {str(e)}', 'error')
        else:
            self.log_callback(f"Skipping results consolidation as asset number is '{asset_number}'.", 'info')

    def get_summary_data(self) -> Dict[str, Any]:
        return self.end_screen_summary_data
if __name__ == '__main__':

    class MockResultManager:
        def add_result(self, asset_number, operator_id, test_name, result):
            print(f"MOCK_RESULT_ADD for asset {asset_number} by {operator_id}: {test_name} - {result.get('status', result.get('test_details', {}).get('status'))}")
        
        def consolidate_results_for_asset(self, asset_number, operator_id):
            print(f"Consolidating for {asset_number}")
            return "mock_path.json"

    class MockApp(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title('Mock App')
            self.withdraw()

    mock_app = MockApp()
    mock_logger = lambda msg, lvl: print(f'LOG [{lvl.upper()}]: {msg}')
    mock_res_manager = MockResultManager()
    sample_profile = Profile(name='Full Test', device_type='laptop', description='Full suite')
    sample_profile.tests = ['agent.tests.cpu_test.run_basic_cpu_test', 'agent.tests.ram_test.run_ram_test', 'agent.tests.drive_wipe_test.run_secure_wipe_test', 'non.existent.test.run_fake_test']
    
    get_profile_cb = lambda: sample_profile
    get_asset_cb = lambda: "MOCK_ASSET_123"
    get_operator_cb = lambda: "MOCK_OPERATOR_456"

    orchestrator = TestOrchestrator(
        log_callback=mock_logger,
        result_manager_instance=mock_res_manager,
        main_app_ref=mock_app,
        get_current_profile_callback=get_profile_cb,
        get_asset_number_callback=get_asset_cb,
        get_operator_id_callback=get_operator_cb
    )

    print('--- Executing Tests ---')
    orchestrator.execute_tests()
    summary = orchestrator.get_summary_data()
    print('\n--- Test Summary ---')
    import json
    print(json.dumps(summary, indent=2))
    mock_app.destroy()
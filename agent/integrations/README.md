# ArcoaEPS Integrations

This directory contains integrations with external systems.

## Makor ERP Integration

The Makor ERP integration allows ArcoaEPS to automatically send hardware audit reports and drive wipe statuses to the Makor ERP system after drives are wiped.

### Features

- Automatically collects system hardware information
- Collects detailed drive information
- Includes drive wipe status and results
- Generates XML audit reports in the format expected by Makor ERP
- Base64 encodes the XML reports
- Sends the reports to Makor ERP via its API with Basic Authentication
- Configurable via a JSON configuration file

### Configuration

The integration is configured via the `makor_config.json` file in this directory. The file has the following structure:

```json
{
    "api_url": "http://your-makor-erp-api-url.com",
    "username": "your_username",
    "password": "your_password",
    "enabled": true
}
```

- `api_url`: The URL of the Makor ERP API
- `username`: The username for Basic Authentication
- `password`: The password for Basic Authentication
- `enabled`: Whether the integration is enabled or disabled

### Integration Points

The integration is automatically triggered after drive wipes are completed in the Drive Wipe GUI. The `all_wipes_completed` method in `agent/gui/drive_wipe_gui.py` calls the `send_wipe_results_to_makor` function from `agent/integrations/makor_erp.py` to send the audit report.

### API Endpoints Used

- `/api/diagnostics/report`: Used to send the Base64-encoded XML audit report

### XML Report Format

The XML report has the following structure:

```xml
<DiagnosticReport>
    <SerialNumber>System Serial Number</SerialNumber>
    <Model>System Model</Model>
    <CPU>CPU Name</CPU>
    <RAM>Total RAM in GB</RAM>
    <GPU>GPU Names</GPU>
    <OS>Operating System</OS>
    <HDD>
        <SerialNumber>Drive Serial Number</SerialNumber>
        <Model>Drive Model</Model>
        <Capacity>Drive Capacity in GB</Capacity>
        <WipeStatus>true/false</WipeStatus>
    </HDD>
    <!-- Additional HDD elements for each drive -->
</DiagnosticReport>
```

### Troubleshooting

If the integration is not working as expected, check the following:

1. Ensure the `makor_config.json` file exists and has the correct structure
2. Verify that the `enabled` field is set to `true`
3. Check that the `api_url`, `username`, and `password` fields are correctly set
4. Look for error messages in the application logs
5. Verify that the Makor ERP API is accessible from the machine running ArcoaEPS

### Platform Compatibility

The integration is designed to work on both Windows and Linux platforms, as it uses the existing system information and drive information collection functions that are already platform-aware.

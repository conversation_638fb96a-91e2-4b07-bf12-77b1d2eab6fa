import xml.etree.ElementTree as ET
import base64
import logging
try:
    import requests
except ImportError:
    requests = None
    logging.error("Module 'requests' is not installed. Makor ERP integration will be disabled.")
import json
import os
from typing import Dict, Any, List

LOGGER = logging.getLogger(__name__)

# --- Configuration for Makor ERP API ---
# Load configuration from JSON file
CONFIG_FILE = os.path.join(os.path.dirname(__file__), "makor_config.json")

# Default configuration values
MAKOR_CONFIG = {
    "api_url": "http://your-makor-erp-api-url.com",
    "username": "your_username",
    "password": "your_password",
    "enabled": True
}

# Try to load configuration from file
try:
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r") as f:
            loaded_config = json.load(f)
            MAKOR_CONFIG.update(loaded_config)
            LOGGER.info(f"Loaded Makor ERP configuration from {CONFIG_FILE}")
    else:
        LOGGER.warning(f"Makor ERP configuration file not found at {CONFIG_FILE}, using defaults")
        # Create default configuration file
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(MAKOR_CONFIG, f, indent=4)
                LOGGER.info(f"Created default Makor ERP configuration file at {CONFIG_FILE}")
        except Exception as e:
            LOGGER.error(f"Failed to create default Makor ERP configuration file: {e}")
except Exception as e:
    LOGGER.error(f"Error loading Makor ERP configuration: {e}")

# Extract configuration values
MAKOR_API_URL = MAKOR_CONFIG.get("api_url")
MAKOR_API_USERNAME = MAKOR_CONFIG.get("username")
MAKOR_API_PASSWORD = MAKOR_CONFIG.get("password")
MAKOR_ENABLED = MAKOR_CONFIG.get("enabled", True)

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def _create_xml_element(parent: ET.Element, tag: str, text: Any = None, attrib: Dict[str, str] = None) -> ET.Element:
    """Helper to create and append an XML element to a parent."""
    element = ET.SubElement(parent, tag, attrib or {})
    if text is not None:
        element.text = str(text)
    return element

def _format_capacity_gb(size_bytes: int | None) -> str:
    """Formats bytes to GB, rounding to 2 decimal places."""
    if size_bytes is None or size_bytes == 0:
        return "0.00 GB"
    return f"{(size_bytes / (1024**3)):.2f} GB"

# ---------------------------------------------------------------------------
# Main functions for Makor ERP integration
# ---------------------------------------------------------------------------

def generate_audit_report_xml(
    system_info: Dict[str, Any],
    drives_info: List[Dict[str, Any]],
    wipe_results: List[Dict[str, Any]]
) -> str:
    """Generates the XML audit report for Makor ERP.

    Args:
        system_info: Dictionary containing system hardware information
                     (from agent.hardware.system_info.get_system_info()).
        drives_info: List of dictionaries with detailed drive information
                     (from agent.hardware.drive_info.get_detailed_drive_info()).
        wipe_results: List of dictionaries with wipe results for each drive
                      (from agent.tests.drive_wipe_test.perform_single_wipe_threaded).

    Returns:
        A string containing the XML report.
    """
    root = ET.Element("DiagnosticReport")

    _create_xml_element(root, "SerialNumber", system_info.get("serial_number", "N/A"))
    _create_xml_element(root, "Model", system_info.get("model", "N/A"))
    _create_xml_element(root, "CPU", system_info.get("cpu", {}).get("name", "N/A"))
    _create_xml_element(root, "RAM", _format_capacity_gb(system_info.get("memory", {}).get("total_bytes")))

    # Handle GPUs
    gpus = system_info.get("gpus", [])
    if gpus:
        gpu_names = ", ".join([gpu.get("name", "N/A") for gpu in gpus])
        _create_xml_element(root, "GPU", gpu_names)
    else:
        _create_xml_element(root, "GPU", "N/A")

    _create_xml_element(root, "OS", system_info.get("os", {}).get("pretty_name", "N/A"))

    # Handle HDDs (drives)
    for drive in drives_info:
        hdd_elem = _create_xml_element(root, "HDD")
        _create_xml_element(hdd_elem, "SerialNumber", drive.get("serial", "N/A"))
        _create_xml_element(hdd_elem, "Model", drive.get("model", "N/A"))
        _create_xml_element(hdd_elem, "Capacity", _format_capacity_gb(drive.get("size_bytes")))

        # Find corresponding wipe result for this drive
        wipe_status = "N/A" # Default if no wipe result found
        for result in wipe_results:
            # Use device_path from wipe_results and path from drives_info for matching
            if result.get("device_path") == drive.get("path"):
                # Map internal status to Makor's expected values (assuming 'success' boolean)
                # Makor API docs indicate 'success' boolean for wipe status. Let's assume 'true'/'false' string.
                wipe_status = "true" if result.get("status") == "pass" else "false"
                break
        _create_xml_element(hdd_elem, "WipeStatus", wipe_status)

    return ET.tostring(root, encoding="utf-8", xml_declaration=True).decode("utf-8")

def send_to_makor_erp(xml_report: str) -> bool:
    """Sends the XML audit report to the Makor ERP API.
    
    Args:
        xml_report: The XML report as a string.
    
    Returns:
        True if the report was successfully sent (HTTP 2xx status), False otherwise.
    """
    if requests is None:
        LOGGER.error("Makor ERP integration is disabled because the 'requests' module is not installed.")
        return False
    encoded_xml = base64.b64encode(xml_report.encode("utf-8")).decode("utf-8")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {base64.b64encode(f'{MAKOR_API_USERNAME}:{MAKOR_API_PASSWORD}'.encode()).decode()}"
    }

    payload = {
        "auditReport": encoded_xml
    }

    endpoint = f"{MAKOR_API_URL}/api/diagnostics/report"

    LOGGER.info(f"Sending audit report to Makor ERP at {endpoint}")
    try:
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        LOGGER.info(f"Audit report successfully sent. Status Code: {response.status_code}")
        LOGGER.debug(f"Makor ERP Response: {response.text}")
        return True
    except requests.exceptions.HTTPError as e:
        LOGGER.error(f"HTTP error sending report to Makor ERP: {e.response.status_code} - {e.response.text}")
    except requests.exceptions.ConnectionError as e:
        LOGGER.error(f"Connection error sending report to Makor ERP: {e}")
    except requests.exceptions.Timeout as e:
        LOGGER.error(f"Timeout sending report to Makor ERP: {e}")
    except requests.exceptions.RequestException as e:
        LOGGER.error(f"An unexpected error occurred while sending report to Makor ERP: {e}")
    except Exception as e:
        LOGGER.error(f"An unexpected error occurred: {e}")
    return False

def send_wipe_results_to_makor(wipe_results: List[Dict[str, Any]]) -> bool:
    """
    Main integration function to be called from the drive wipe GUI after wipe completion.
    Collects system info, drive info, generates XML report and sends it to Makor ERP.
    
    Args:
        wipe_results: List of dictionaries with wipe results for each drive
                      (from agent.tests.drive_wipe_test.perform_single_wipe_threaded)
                      or collected in the GUI's finalize_drive_wipe method.
    
    Returns:
        True if the report was successfully sent, False otherwise.
    """
    # Check if Makor ERP integration is enabled
    if not MAKOR_ENABLED:
        LOGGER.info("Makor ERP integration is disabled in configuration. Skipping audit report.")
        return False
        
    # Check if API URL, username, and password are configured
    if not MAKOR_API_URL or not MAKOR_API_USERNAME or not MAKOR_API_PASSWORD:
        LOGGER.error("Makor ERP API URL, username, or password not configured. Cannot send audit report.")
        return False
    
    try:
        # Import here to avoid circular imports
        from agent.hardware.system_info import get_system_info
        from agent.hardware.drive_info import get_detailed_drive_info
        
        LOGGER.info("Collecting system information for Makor ERP audit report...")
        system_info = get_system_info()
        
        LOGGER.info("Collecting drive information for Makor ERP audit report...")
        drives_info = get_detailed_drive_info()
        
        LOGGER.info("Generating XML audit report...")
        xml_report = generate_audit_report_xml(system_info, drives_info, wipe_results)
        
        LOGGER.info("Sending audit report to Makor ERP...")
        success = send_to_makor_erp(xml_report)
        
        if success:
            LOGGER.info("Successfully sent audit report to Makor ERP")
        else:
            LOGGER.error("Failed to send audit report to Makor ERP")
            
        return success
    except Exception as e:
        LOGGER.error(f"Error sending wipe results to Makor ERP: {e}")
        return False

if __name__ == "__main__":
    # Example Usage (for testing purposes)
    logging.basicConfig(level=logging.INFO)

    # Dummy data for demonstration
    dummy_system_info = {
        "serial_number": "SN123456789",
        "model": "Laptop XYZ",
        "cpu": {"name": "Intel Core i7"},
        "memory": {"total_bytes": 17179869184}, # 16 GB
        "gpus": [{"name": "NVIDIA GeForce RTX 3060"}],
        "os": {"pretty_name": "Windows 10 Pro"},
    }

    dummy_drives_info = [
        {
            "path": r"\\.\PHYSICALDRIVE0",
            "model": "NVMe SSD 1TB",
            "serial": "SSDSERIAL001",
            "size_bytes": 1000204886016,
            "type": "NVMe"
        },
        {
            "path": r"\\.\PHYSICALDRIVE1",
            "model": "SATA HDD 2TB",
            "serial": "HDDSERIAL002",
            "size_bytes": 2000398934016,
            "type": "HDD"
        }
    ]

    dummy_wipe_results = [
        {
            "device_path": r"\\.\PHYSICALDRIVE0",
            "method": "nvme_sanitize",
            "status": "pass",
            "details": "NVMe sanitize complete",
            "errors": ""
        },
        {
            "device_path": r"\\.\PHYSICALDRIVE1",
            "method": "zero_fill",
            "status": "fail",
            "details": "Zero-fill failed",
            "errors": "dd returned 1"
        }
    ]

    print("\n--- Generating XML Report ---")
    xml_report = generate_audit_report_xml(dummy_system_info, dummy_drives_info, dummy_wipe_results)
    print(xml_report)

    print("\n--- Attempting to Send Report (will fail with placeholder URL) ---")
    # This call will likely fail because MAKOR_API_URL is a placeholder.
    # Replace MAKOR_API_URL, MAKOR_API_USERNAME, MAKOR_API_PASSWORD with actual values to test.
    success = send_to_makor_erp(xml_report)
    print(f"Report sent successfully: {success}")

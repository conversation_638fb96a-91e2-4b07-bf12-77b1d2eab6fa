#!/usr/bin/env python3
"""
Arcoa Nexus Network Utilities

This module handles network communications with the Arcoa Nexus server
including sending test results and checking server connectivity.
"""
import json
from typing import <PERSON><PERSON>, Dict, Any

import httpx
from agent.nexus_client import NexusClient


async def send_result(server_url: str, result_data: Dict[str, Any]) -> Tuple[bool, str]:
    """Send a test result to the server asynchronously using NexusClient.

    Falls back to local queue when the server is unreachable so tests can be
    run offline during prototyping.  Returns the tuple from
    :py:meth:`agent.nexus_client.NexusClient.post_result_async`.
    """
    client = NexusClient(server_url)
    return await client.post_result_async(result_data)


async def check_server_status(server_url: str) -> Tuple[bool, str]:
    """
    Check if the server is alive and reachable.
    
    Args:
        server_url: URL of the Arcoa Nexus server
        
    Returns:
        Tuple of (is_online, message)
    """
    try:
        if not server_url.endswith("/"):
            server_url += "/"
            
        # Ensure we have a protocol
        if not server_url.startswith("http"):
            server_url = "http://" + server_url
            
        url = f"{server_url}health/"
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                url,
                timeout=5.0  # 5 second timeout 
            )
            
        if response.status_code == 200:
            return True, "Server is online"
        else:
            return False, f"Server returned unexpected status: {response.status_code}"
            
    except httpx.ConnectError:
        return False, "Could not connect to server"
    except httpx.TimeoutException:
        return False, "Server connection timed out"
    except Exception as e:
        return False, f"Error checking server status: {str(e)}"

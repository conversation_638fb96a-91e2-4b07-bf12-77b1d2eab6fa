"""
Arcoa Nexus Device Condition Window

This module provides a GUI for setting and recording the physical condition of devices being tested.
"""
import json
import os
import tkinter as tk
from agent.core.device_condition_manager import get_conditions_file_path, save_device_conditions, load_device_conditions
from agent.gui.theme import COLORS
from tkinter import ttk, messagebox
from typing import Any, Callable, Dict, Optional

class DeviceConditionWindow(tk.Toplevel):
    """Window for setting device physical condition parameters."""

    def __init__(self, parent, callback: Optional[Callable]=None, current_conditions: Optional[Dict[str, Any]]=None):
        """
        Initialize the device condition window.

        Args:
            parent: Parent window
            callback: Optional callback function to call when conditions are saved
            current_conditions: Optional dictionary of current condition values
        """
        super().__init__(parent)
        self.parent = parent
        self.callback = callback
        self.title('Device Condition Assessment')
        self.geometry('600x550')
        self.minsize(600, 550)
        self.transient(parent)
        self.configure(bg=COLORS['bg_dark'])
        self.conditions = {'case_condition': tk.StringVar(value='Good'), 'screen_condition': tk.StringVar(value='Good'), 'missing_items': tk.StringVar(value='N/A'), 'keyboard_condition': tk.StringVar(value='Good'), 'touchpad_condition': tk.StringVar(value='Good'), 'usb_ports': tk.StringVar(value='Good'), 'grade': tk.StringVar(value='A')}
        if current_conditions:
            for key, value in current_conditions.items():
                if key in self.conditions:
                    self.conditions[key].set(value)
        self.create_ui()
        self.update_idletasks()
        self.deiconify()
        self.after(10, self._safe_grab_focus)

    def _safe_grab_focus(self):
        """Safely grab focus after window is fully visible."""
        try:
            if self.winfo_exists() and self.winfo_viewable():
                self.grab_set()
                self.focus_set()
                self.lift()
            else:
                self.after(10, self._safe_grab_focus)
        except tk.TclError as e:
            print(f'Warning: Could not grab focus for DeviceConditionWindow: {e}')

    def create_ui(self):
        """Create the user interface."""
        main_frame = ttk.Frame(self, padding='10')
        main_frame.pack(fill=tk.BOTH, expand=True)
        title_label = ttk.Label(main_frame, text='Device Condition Assessment', font=('Arial', 16, 'bold'), foreground=COLORS['accent'])
        title_label.pack(pady=(0, 10))
        conditions_frame = ttk.Frame(main_frame)
        conditions_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        condition_options = {'case_condition': {'label': 'Case Condition:', 'options': ['Good', 'Light Scratches', 'Heavy Scratches', 'Light Dents', 'Heavy Dents', 'Cracks']}, 'screen_condition': {'label': 'Screen Condition:', 'options': ['Good', 'Light Scratches', 'Heavy Scratches', 'Cracked', 'Pressure Marks', 'Faded Areas', 'Dead Pixels']}, 'missing_items': {'label': 'Missing Items:', 'options': ['N/A', 'Point Stick', 'Footpads', 'Keyboard Keys', 'Bottom Screws']}, 'keyboard_condition': {'label': 'Keyboard Condition:', 'options': ['Good', 'Light Wear', 'Heavy Wear']}, 'touchpad_condition': {'label': 'Touchpad Condition:', 'options': ['Good', 'Light Wear', 'Heavy Wear']}, 'usb_ports': {'label': 'USB Ports:', 'options': ['Good', 'Damaged']}, 'grade': {'label': 'Grade:', 'options': ['A', 'B', 'C', 'Damaged']}}
        row = 0
        for key, config in condition_options.items():
            ttk.Label(conditions_frame, text=config['label'], font=('Arial', 12), foreground=COLORS['text_light']).grid(row=row, column=0, sticky=tk.W, padx=5, pady=10)
            if key == 'missing_items':
                self.missing_items_listbox = tk.Listbox(conditions_frame, selectmode=tk.MULTIPLE, height=len(config['options']), font=('Arial', 12))
                for option in config['options']:
                    self.missing_items_listbox.insert(tk.END, option)
                current_val = self.conditions[key].get()
                if current_val != 'N/A':
                    for idx, option in enumerate(config['options']):
                        if option in [s.strip() for s in current_val.split(',')]:
                            self.missing_items_listbox.selection_set(idx)
                self.missing_items_listbox.grid(row=row, column=1, sticky=tk.W, padx=5, pady=10)
            else:
                dropdown = ttk.Combobox(conditions_frame, textvariable=self.conditions[key], values=config['options'], width=20, font=('Arial', 12), state='readonly')
                dropdown.grid(row=row, column=1, sticky=tk.W, padx=5, pady=10)
            row += 1
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        save_button = ttk.Button(buttons_frame, text='Save Conditions', command=self.save_conditions, style='TButton')
        save_button.pack(side=tk.RIGHT, padx=5)
        cancel_button = ttk.Button(buttons_frame, text='Cancel', command=self.on_cancel, style='TButton')
        cancel_button.pack(side=tk.RIGHT, padx=5)

    def save_conditions(self):
        """Save the device conditions and close the window."""
        conditions_data = {key: var.get() for key, var in self.conditions.items()}
        if hasattr(self, 'missing_items_listbox'):
            selections = self.missing_items_listbox.curselection()
            missing = ','.join([self.missing_items_listbox.get(i) for i in selections]) if selections else 'N/A'
            conditions_data['missing_items'] = missing
        if self.callback:
            self.callback(conditions_data)
        self.grab_release()
        self.destroy()

    def on_cancel(self):
        """Cancel and close the window."""
        self.grab_release()
        self.destroy()
if __name__ == '__main__':
    root = tk.Tk()
    root.withdraw()

    def on_save(conditions):
        print('Saved conditions:', conditions)
        save_device_conditions(conditions)
    current = load_device_conditions()
    window = DeviceConditionWindow(root, callback=on_save, current_conditions=current)
    root.mainloop()
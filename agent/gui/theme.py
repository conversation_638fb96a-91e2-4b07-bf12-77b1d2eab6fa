#!/usr/bin/env python3
"""
Arcoa Nexus Theme

This module provides theme colors and styling for the Arcoa Nexus application.
"""

# Styled colors
COLORS = {
    # --- Pointing Device Test Theme Additions ---
    "pointing_test_bg": "#121212",  # Main background for pointing device test
    "pointing_test_canvas_bg": "#121212",  # <PERSON>vas background
    "pointing_test_label_fg": "#FFFFFF",  # Label foreground
    "pointing_test_button_bg": "#FF5BFF",  # Button background
    "pointing_test_button_fg": "#FFFFFF",  # Button foreground
    "pointing_test_status_pass_fg": "#00FF00",  # Status label PASS
    "pointing_test_status_fail_fg": "#FF3333",  # Status label FAIL
    "pointing_test_status_pending_fg": "#FFFF00",  # Status label pending/neutral
    "pointing_test_status_bg": "#121212",  # Status label background
    "debug_fg": "#00BFFF",  # Blue for debug log messages

    "primary": "#325796",  # Red highlight
    "success": "#4CAF50",
    "warning": "#FF0000",
    "error": "#F31D1D",  # Orange-red for errors/highlights
    "bg_light": "#333232",  # Dark gray
    "bg_dark": "#1a1919",  # Even darker for contrast
    "text_light": "#FFFFFF",
    "text_dark": "#CCCCCC",
    "accent": "#3d3d3d",
    "title": "#FFFFFF",
    "button": "#313131",
    "button_hover": "#504F4F",
    "button_active": "#313131",
    "button_disabled": "#242424",
    "button_text": "#DFBD00",
    "button_text_hover": "#FFFFFF",
    "button_text_active": "#DFBD00",
    "button_text_disabled": "#181818",

    # Responsive font sizes for different resolution ranges
    "tiny_font_size": 6,        # For very small screens or dense layouts
    "small_font_size": 8,       # For low resolution screens (1024x768)
    "medium_font_size": 10,     # For medium resolution screens (1440x900)
    "normal_font_size": 12,     # For standard resolution screens (1920x1080)
    "large_font_size": 14,      # For high resolution screens (2560x1440)
    "xlarge_font_size": 16,     # For very high resolution screens (4K+)

    # Legacy font size definitions (for backward compatibility)
    "low_res_font_size": 8,
    "high_res_font_size": 12,

    # Resolution breakpoints
    "minimum_resolution": (1024, 768),
    "medium_resolution": (1440, 900),
    "high_resolution": (1920, 1080),
    "very_high_resolution": (2560, 1440),

    "font_family": "Consolas",
    #system info colors
    "serial_color": "#DFBD00",
    "cpu_model": "#88ddFF",
    "cpu_speed": "#32a852",
    "memory": "#d1363c",
    "gpu": "#dd7c88",
    "resolution": "#99FFcc",
    "battery_percent": "#55dd55",
    "battery_status": "#55dd55",
    "battery_health": "#55dd55",
    "battery_cycles": "#55dd55",
    "no_battery": "#FF0000",
    "disk": "#55dd55",
    "disk_model": "#9999FF",
    "disk_size": "#cccc33",
    "disk_type": "#55dd55",
    "disk_status_ok": "#55dd55",
    "disk_status_bad": "#FF0000",
    "disk_status_unknown": "#FFcc54",





}

KEYBOARD_TEST_THEME = {
    "bg_color": "#1d1d1d",
    "key_color_bg": "#333333",
    "key_color_fg": "#ffffff",
    "key_color_bg_pressed": "#228822",
    "key_color_fg_pressed": "#000000",
    "font": "Consolas",
    "font_size": 6,
    "border_width": 1,
    "relief": "raised",
    "anchor": "center"
}

FONTS = {
    # --- Pointing Device Test Theme Additions ---
    "pointing_test_font": ("Consolas", 6),
    "pointing_test_status_font": ("Consolas", 6, "bold"),

    "normal_font": "Consolas",
    "small_font": "Consolas",
    "large_font": "Consolas",
    "log_font": "Consolas",
    "log_font_size": 8,
    "profile_font": "Consolas",
    "profile_font_size": 10,
}

SIZES = {
    # Log panel sizing
    "log_window_height": 1,
    "log_window_height_small": 8,   # For small screens
    "log_window_height_medium": 12, # For medium screens
    "log_window_height_large": 15,  # For large screens

    # Responsive padding values
    "padding_tiny": 2,
    "padding_small": 5,
    "padding_medium": 10,
    "padding_large": 15,
    "padding_xlarge": 20,

    # Component-specific sizing
    "button_padding": 8,
    "frame_padding": 5,
    "entry_width_small": 20,
    "entry_width_medium": 30,
    "entry_width_large": 40,

    # Spacing between elements
    "element_spacing_small": 3,
    "element_spacing_medium": 5,
    "element_spacing_large": 8,
}

# Resolution-based scaling function
def get_font_size_for_resolution(width, height):
    """
    Calculate appropriate font size based on screen resolution.
    Returns a font size that scales appropriately with screen size.
    """
    # Calculate total pixels
    total_pixels = width * height

    # Define breakpoints based on common resolutions
    if total_pixels <= 786432:  # 1024x768 and below
        return COLORS["small_font_size"]
    elif total_pixels <= 1296000:  # 1440x900 and below
        return COLORS["medium_font_size"]
    elif total_pixels <= 2073600:  # 1920x1080 and below
        return COLORS["normal_font_size"]
    elif total_pixels <= 3686400:  # 2560x1440 and below
        return COLORS["large_font_size"]
    else:  # 4K and above
        return COLORS["xlarge_font_size"]

def get_padding_for_resolution(width, height):
    """
    Calculate appropriate padding based on screen resolution.
    Returns padding values that scale with screen size.
    """
    total_pixels = width * height

    if total_pixels <= 786432:  # 1024x768 and below
        return {
            "large": SIZES["padding_small"],
            "medium": SIZES["padding_tiny"],
            "small": SIZES["padding_tiny"]
        }
    elif total_pixels <= 1296000:  # 1440x900 and below
        return {
            "large": SIZES["padding_medium"],
            "medium": SIZES["padding_small"],
            "small": SIZES["padding_tiny"]
        }
    elif total_pixels <= 2073600:  # 1920x1080 and below
        return {
            "large": SIZES["padding_large"],
            "medium": SIZES["padding_medium"],
            "small": SIZES["padding_small"]
        }
    else:  # Higher resolutions
        return {
            "large": SIZES["padding_xlarge"],
            "medium": SIZES["padding_large"],
            "small": SIZES["padding_medium"]
        }

def get_log_height_for_resolution(width, height):
    """
    Calculate appropriate log panel height based on screen resolution.
    """
    total_pixels = width * height

    if total_pixels <= 786432:  # 1024x768 and below
        return SIZES["log_window_height_small"]
    elif total_pixels <= 2073600:  # 1920x1080 and below
        return SIZES["log_window_height_medium"]
    else:  # Higher resolutions
        return SIZES["log_window_height_large"]

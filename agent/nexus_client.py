#!/usr/bin/env python3
"""agent.nexus_client

Centralised client for communicating with a Nexus server.

Features
--------
1. Provides *sync* and *async* helpers for posting a test/result payload
   to the Nexus REST API.
2. Transparently falls back to an on-disk spool queue when Nexus is
   unreachable so that data is never lost during prototype/offline
   work.  The queue is retried (FIFO) every time a new result is
   successfully submitted.
3. Uses an environment variable *NEXUS_URL* or value passed at
   construction time.  Defaults to ``http://127.0.0.1:8000`` so that the
   agent continues to run even when the real Nexus host isn’t known
   yet.
4. Queue directory can be overridden by *NEXUS_QUEUE_DIR* but defaults
   to ``~/.arcoa_nexus_queue``.

This file purposefully has **zero** third-party dependencies beyond
``httpx`` (already used elsewhere in the project).
"""
from __future__ import annotations

import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Tuple

import httpx

__all__ = [
    "NexusClient",
    "QUEUE_DIR",
]

# ---------------------------------------------------------------------------
# Configuration helpers
# ---------------------------------------------------------------------------

def _default_queue_dir() -> Path:
    """Return default spool directory creating it if needed."""
    d = Path(os.getenv("NEXUS_QUEUE_DIR", Path.home() / ".arcoa_nexus_queue"))
    d.mkdir(parents=True, exist_ok=True)
    return d


QUEUE_DIR: Path = _default_queue_dir()

# ---------------------------------------------------------------------------
# Nexus client implementation
# ---------------------------------------------------------------------------


class NexusClient:
    """Lightweight wrapper around the Nexus REST API."""

    def __init__(self, base_url: str | None = None, *, timeout: float = 10.0):
        self.base_url: str = (base_url or os.getenv("NEXUS_URL", "http://127.0.0.1:8000")).rstrip("/")
        # Ensure scheme – if the user only typed an IP we prepend http://
        if not self.base_url.startswith(("http://", "https://")):
            self.base_url = "http://" + self.base_url

        self.timeout: float = timeout
        # Re-use an httpx.Client for connection pooling.  (Not created for
        # async variant.)
        self._client = httpx.Client(timeout=self.timeout)

    # ---------------------------------------------------------------------
    # Public helpers
    # ---------------------------------------------------------------------

    def post_result(self, result: Dict[str, Any]) -> Tuple[bool, str]:
        """Synchronously POST a single *result* JSON payload.

        On success, returns ``(True, "OK")``.
        On failure, spools the payload to :pydata:`QUEUE_DIR` and returns
        ``(False, "<reason>")``.
        """
        try:
            resp = self._client.post(self._full_url("/result"), json=result)
            if resp.status_code == 200:
                # Success – now that server is reachable try to flush backlog
                flushed = self.flush_queue()
                msg = "OK" if flushed == 0 else f"OK (+flushed {flushed})"
                return True, msg
            return False, f"HTTP {resp.status_code}: {resp.text}"
        except Exception as exc:  # noqa: BLE001 – we want to capture everything
            logging.warning("Nexus unreachable (%s). Spooling result.", exc)
            self._queue_result(result)
            return False, f"queued offline: {exc}"

    async def post_result_async(self, result: Dict[str, Any]) -> Tuple[bool, str]:
        """Asynchronous variant of :py:meth:`post_result`."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                resp = await client.post(self._full_url("/result"), json=result)

            if resp.status_code == 200:
                flushed = self.flush_queue()
                msg = "OK" if flushed == 0 else f"OK (+flushed {flushed})"
                return True, msg
            return False, f"HTTP {resp.status_code}: {resp.text}"
        except Exception as exc:  # noqa: BLE001
            logging.warning("Nexus unreachable (%s). Spooling result.", exc)
            self._queue_result(result)
            return False, f"queued offline: {exc}"

    def flush_queue(self) -> int:
        """Attempt to POST all queued files.  Returns number of files sent."""
        sent = 0
        for path in sorted(QUEUE_DIR.glob("*.json")):
            try:
                data = json.loads(path.read_text())
                ok, _ = self.post_result(data)
                if ok:
                    path.unlink(missing_ok=True)
                    sent += 1
            except Exception as exc:  # noqa: BLE001
                logging.error("Error flushing queue file %s: %s", path, exc)
        return sent

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    def _full_url(self, path: str) -> str:  # noqa: D401 – simple helper
        """Return *self.base_url* plus *path* (which should start with '/')."""
        return f"{self.base_url}{path}"

    @staticmethod
    def _timestamp_ms() -> int:
        return int(time.time() * 1000)

    def _queue_result(self, payload: Dict[str, Any]) -> None:
        fname = QUEUE_DIR / f"{self._timestamp_ms()}.json"
        try:
            fname.write_text(json.dumps(payload, indent=2))
        except Exception as exc:  # noqa: BLE001
            logging.error("Failed to write spool file %s: %s", fname, exc)

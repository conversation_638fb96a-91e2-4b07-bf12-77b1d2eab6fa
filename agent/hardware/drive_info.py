#!/usr/bin/env python3
"""agent.hardware.drive_info

Drive discovery and SMART details for Arcoa Nexus.

This module abstracts low-level CLI interactions (lsblk, smartctl, etc.) and
provides easy-to-consume Python dictionaries. It is Linux-centric and assumes
root privileges, but does its best to fail gracefully if tools are missing
or the underlying commands return non-zero status while still emitting JSON.
"""
from __future__ import annotations

import json
import logging
import shutil
import subprocess
from typing import Any, Dict, List
import platform
import os

LOGGER = logging.getLogger(__name__)

# --- Globals for caching CLI tool paths ---
_smartctl_executable_path: str | None = None
_smartctl_checked: bool = False

# ---------------------------------------------------------------------------
# Helper utilities
# ---------------------------------------------------------------------------

def _run_cmd_json(cmd: List[str]) -> Any:
    """Run *cmd* and attempt to parse its stdout as JSON.

    If the command exits with non-zero status yet still prints something that
    looks like JSON, we try to parse it anyway – SMART tools often do this
    when the device does not fully support a feature but still emit useful
    data.

    Returns None if parsing fails.
    """
    try:
        LOGGER.debug("Running command: %s", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        out = result.stdout.strip()
        if not out:
            LOGGER.warning("No output from command %s", cmd[0])
            return None
        
        # Log the full output for debugging, especially for lsblk
        if cmd[0] == 'lsblk':
            LOGGER.info(f"Full lsblk output (first 1000 chars):\n{out[:1000]}")
            if len(out) > 1000:
                LOGGER.info(f"... (truncated, total length: {len(out)} chars)")

        try:
            return json.loads(out)
        except json.JSONDecodeError as exc:
            LOGGER.error("Failed to parse JSON from %s: %s", cmd[0], exc)
            LOGGER.debug("Raw output:\n%s", out[:1000])
            return None
    except FileNotFoundError:
        LOGGER.error("Required CLI tool not found: %s", cmd[0])
        return None

# ---------------------------------------------------------------------------
# Public API functions
# ---------------------------------------------------------------------------

def get_lsblk_info() -> List[Dict[str, Any]]:
    """Return lsblk ‑J JSON list of block devices (disks + partitions) on Linux,
    or a simulated list of physical disks on Windows."""
    system = platform.system()
    if system == "Linux":
        cmd = [
            "lsblk",
            "-Jbp", # -b for bytes, -p for full paths, -J for JSON
            "-o",
            "NAME,PATH,MODEL,SERIAL,SIZE,TYPE,ROTA,TRAN,VENDOR,WWN,FSTYPE,MOUNTPOINT,PKNAME",
        ]
        data = _run_cmd_json(cmd)
        if not data or "blockdevices" not in data:
            return []
        return data["blockdevices"]
    elif system == "Windows":
        disks_info = []
        try:
            try:
                import psutil
            except ImportError:
                LOGGER.error("psutil library is required on Windows for drive_info but not found.")
                return []

            ps_command_disks = [
                "powershell",
                "-NoProfile",
                "-Command",
                "Get-WmiObject Win32_DiskDrive | Select-Object DeviceID, Model, SerialNumber, Size, InterfaceType, Manufacturer, PNPDeviceID, Index | ConvertTo-Json -Compress"
            ]
            wmic_data_raw = _run_cmd_json(ps_command_disks)

            if not wmic_data_raw:
                LOGGER.warning("Failed to get disk drive info from WMIC/PowerShell on Windows.")
                return []

            wmic_disks = wmic_data_raw if isinstance(wmic_data_raw, list) else [wmic_data_raw]
            all_psutil_partitions = psutil.disk_partitions(all=True)

            for disk_wmic in wmic_disks:
                path = disk_wmic.get("DeviceID")
                if not path:
                    continue

                disk_entry: Dict[str, Any] = {
                    "path": path,
                    "name": path,
                    "model": (disk_wmic.get("Model") or "Unknown").strip() or "Unknown",
                    "serial": (disk_wmic.get("SerialNumber") or "Unknown").strip() or "Unknown",
                    "size": str(disk_wmic.get("Size") or "0"),
                    "type": "disk",
                    "vendor": (disk_wmic.get("Manufacturer") or "").strip(),
                    "rota": None,
                    "tran": None,
                    "children": [],
                    "mountpoint": None # Physical disks usually don't have a single mountpoint
                }

                interface_type = (disk_wmic.get("InterfaceType") or "").lower()
                if "nvme" in interface_type:
                    disk_entry["tran"] = "nvme"
                    disk_entry["rota"] = "0"
                elif "usb" in interface_type:
                    disk_entry["tran"] = "usb"
                    disk_entry["rota"] = "0"
                elif any(it in interface_type for it in ["ide", "ata", "sata"]):
                    disk_entry["tran"] = "sata"
                    disk_entry["rota"] = "0" # Defaulting to SSD; HDD would be "1". Needs MSFT_PhysicalDisk for MediaType for accuracy.
                elif any(it in interface_type for it in ["scsi", "sas"]):
                    disk_entry["tran"] = "scsi" # or "sas"
                    disk_entry["rota"] = "0" # Defaulting, could be HDD

                disk_index_wmic = disk_wmic.get("Index")
                if disk_index_wmic is not None:
                    ps_partitions_cmd = [
                        "powershell",
                        "-NoProfile",
                        "-Command",
                        (f"Get-Disk -Number {disk_index_wmic} | Get-Partition | "
                         "Select-Object DiskNumber, PartitionNumber, DriveLetter, AccessPaths, Size, Offset, Type, IsBoot, IsSystem, IsReadOnly | "
                         "ConvertTo-Json -Compress")
                    ]
                    partitions_data_raw = _run_cmd_json(ps_partitions_cmd)
                    if partitions_data_raw:
                        partitions_on_disk = partitions_data_raw if isinstance(partitions_data_raw, list) else [partitions_data_raw]
                        for part_ps in partitions_on_disk:
                            part_mountpoint = None
                            drive_letter = part_ps.get("DriveLetter")
                            if drive_letter:
                                part_mountpoint = f"{drive_letter}:\\"
                            elif isinstance(part_ps.get("AccessPaths"), list) and part_ps["AccessPaths"]:
                                for ap_path in part_ps["AccessPaths"]:
                                    if isinstance(ap_path, str) and (":" in ap_path or os.path.isdir(ap_path)):
                                        part_mountpoint = ap_path
                                        break
                            
                            partition_path_simulated = f"{path}-Part{part_ps.get('PartitionNumber', 'N/A')}"
                            child_entry: Dict[str, Any] = {
                                "path": partition_path_simulated,
                                "name": partition_path_simulated,
                                "mountpoint": part_mountpoint,
                                "size": str(part_ps.get("Size") or "0"),
                                "type": "part",
                                "fstype": None
                            }
                            if part_mountpoint:
                                for p_psutil in all_psutil_partitions:
                                    if p_psutil.mountpoint == part_mountpoint:
                                        child_entry["fstype"] = p_psutil.fstype
                                        break
                            disk_entry["children"].append(child_entry)
                disks_info.append(disk_entry)

        except FileNotFoundError:
            LOGGER.error("PowerShell not found on Windows. Cannot get disk info.")
            return []
        except ImportError: # Should have been caught earlier, but as a safeguard
            LOGGER.error("psutil library is missing for Windows drive_info.")
            return []
        except Exception as e:
            LOGGER.error(f"Error collecting disk info on Windows: {e}", exc_info=True)
            return []
        return disks_info
    else:
        LOGGER.warning(f"Unsupported platform for get_lsblk_info: {system}")
        return []


def get_smart_info(device_path: str) -> Dict[str, Any]:
    """Return parsed JSON from smartctl for *device_path*.

    If the first invocation without the *-d* flag fails to produce parseable
    JSON, we will retry with *-d nvme* assuming the device could be NVMe.
    Checks for smartctl existence only once.
    """
    global _smartctl_executable_path, _smartctl_checked

    if not _smartctl_checked:
        _smartctl_executable_path = shutil.which("smartctl")
        if not _smartctl_executable_path:
            LOGGER.error(
                "Required CLI tool 'smartctl' not found in PATH. "
                "SMART data will not be available. Please install smartmontools."
            )
        _smartctl_checked = True

    if not _smartctl_executable_path:
        return {} # smartctl not available, return empty dict

    # Use the found executable path
    base_cmd = [_smartctl_executable_path, "-a", "-j", "--nocheck", "standby", device_path]
    data = _run_cmd_json(base_cmd)
    if data is None and platform.system() == "Linux": # NVMe retry is often more relevant on Linux
        # Retry with explicit NVMe type if initial attempt fails (common for NVMe on Linux)
        nvme_cmd = [_smartctl_executable_path, "-a", "-j", "-d", "nvme", "--nocheck", "standby", device_path]
        data = _run_cmd_json(nvme_cmd)
    elif data is None and platform.system() == "Windows":
        # On Windows, smartctl often auto-detects NVMe correctly, but if it fails,
        # trying with -d nvme might still be useful for some controllers.
        # However, be cautious as incorrect -d can also cause issues.
        # For now, we'll keep it simple and not auto-retry -d nvme on Windows if the first fails,
        # unless specific issues arise that warrant it.
        pass # Not retrying with -d nvme on Windows by default after first failure

    return data or {}


def _infer_drive_type(rota: int | None, tran: str | None) -> str:
    """Infer human-friendly drive type from rota + tran fields."""
    # Ensure rota is an int if it's a string digit
    if isinstance(rota, str) and rota.isdigit():
        rota = int(rota)
    elif rota is None: # If rota is None (e.g. from Windows data), make a guess or default
        if tran == "nvme": return "NVMe"
        if tran == "sata" or tran == "scsi" or tran == "ide": return "SSD" # Default guess for these types if ROTA unknown
        if tran == "usb": return "USB" # Could be SSD or HDD, default to generic USB
        return "Unknown" # Default if not enough info

    if tran == "nvme":
        return "NVMe"
    if rota == 0: # Typically means non-rotational (SSD)
        return "SSD"
    if rota == 1: # Typically means rotational (HDD)
        if tran == "usb": return "USB HDD"
        return "HDD"
    # Fallbacks for less common ROTA values or if tran is also generic
    if tran == "usb":
        return "USB"
    return "HDD" # Default assumption if ROTA is not 0 or 1 and not NVMe/USB


def get_detailed_drive_info() -> List[Dict[str, Any]]:
    LOGGER.debug('Entering get_detailed_drive_info()')
    """Return a list of dictionaries with enriched drive information suitable
    for GUI display and secure-wipe logic."""
    lsblk_devs = get_lsblk_info()
    detailed: List[Dict[str, Any]] = []
    for dev in lsblk_devs:
        if dev.get("type") != "disk" or dev.get("name", "").startswith("/dev/zram"): # Filter out non-disk types and zram devices
            continue  # skip partitions / LVM etc.

        path = dev.get("path") or dev.get("name")
        if not path:
            continue

        # Size may be a string (bytes) or already int/None. Ensure robust parsing.
        size_raw = dev.get("size", 0)
        try:
            size_bytes = int(size_raw)
        except (ValueError, TypeError):
            size_bytes = 0
        size_gb = round(size_bytes / (1024 ** 3), 2) if size_bytes else None

        drive_data: Dict[str, Any] = {
            "path": path,
            "model": dev.get("model", "Unknown").strip() or "Unknown",
            "serial": dev.get("serial", "Unknown").strip() or "Unknown",
            "size_bytes": size_bytes,
            "size_gb": size_gb,
            "vendor": dev.get("vendor"),
            "wwn": dev.get("wwn"),
            "rota": dev.get("rota"),
            "tran": dev.get("tran"),
            "type": _infer_drive_type(dev.get("rota"), dev.get("tran")),
            "mountpoints": [],
            "partitions": [],
            "smart": {},
            "smart_passed": None,
        }

        # Collect mountpoints for the disk and its children
        if dev.get("mountpoint"):
            drive_data["mountpoints"].append(dev["mountpoint"])
        for child in dev.get("children", []):
            if child.get("mountpoint"):
                drive_data["mountpoints"].append(child["mountpoint"])
                drive_data["partitions"].append(child.get("path") or child.get("name"))

        # SMART data
        smart_data = get_smart_info(path)
        drive_data["smart"] = smart_data
        drive_data["smart_passed"] = bool(smart_data.get("smart_status", {}).get("passed")) if smart_data else None

        # Note: SMART information was already collected above via get_smart_info.
        # Do NOT append extra placeholder drives here; that previously caused duplicate
        # "Unknown" entries and exceptions when lsblk returned human-readable sizes.
        # Any additional Linux-specific enrichment should be added above without
        # altering the final `detailed` list structure.
        detailed.append(drive_data)

    return detailed

import datetime
import json
import os
import platform
import re
import subprocess
import shutil
import logging
from typing import Dict, Any, List
import psutil
from agent.hardware.drive_info import get_detailed_drive_info
'\nArcoa Nexus Hardware Information Module\n\nThis module handles collecting system hardware information including:\n- CPU, RAM, disk details\n- GPU information\n- Battery status\n- Serial numbers and system identifiers\n'
try:
    import cpuinfo
except ImportError:
    cpuinfo = None
LOGGER = logging.getLogger(__name__)

def get_serial_number() -> str:
    """Get system serial number."""
    serial = 'Unknown'
    try:
        if platform.system() == 'Windows':
            try:
                result = subprocess.run(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'bios', 'get', 'serialnumber'], capture_output=True, text=True, check=True)
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except Exception:
                pass
            try:
                result = subprocess.run(['powershell', '-Command', '(Get-WmiObject -Class Win32_BIOS).SerialNumber'], capture_output=True, text=True, check=True)
                return result.stdout.strip() or 'Unknown'
            except Exception:
                return 'Unknown'
        elif platform.system() == 'Linux':
            sys_paths_to_try = ['/sys/class/dmi/id/product_serial', '/sys/class/dmi/id/board_serial', '/sys/firmware/devicetree/base/serial-number', '/sys/devices/virtual/dmi/id/product_serial']
            for path in sys_paths_to_try:
                try:
                    with open(path, 'r') as f:
                        content = f.read().strip()
                    if content and content != 'None' and (content != 'To be filled by O.E.M.'):
                        serial = content
                        return serial
                except FileNotFoundError:
                    continue
                except PermissionError:
                    LOGGER.warning(f'Permission denied reading serial from {path}')
                    continue
                except Exception as e:
                    LOGGER.warning(f'Error reading serial from {path}: {e}')
                    continue
            try:
                if shutil.which('lshw') is not None:
                    result = subprocess.run(['lshw', '-c', 'system'], capture_output=True, text=True, check=False)
                    if result.returncode == 0:
                        for line in result.stdout.splitlines():
                            if 'serial:' in line.lower():
                                serial_part = line.split(':', 1)[1].strip()
                                if serial_part and serial_part != 'None' and (serial_part != 'To be filled by O.E.M.'):
                                    return serial_part
                    LOGGER.info('lshw did not provide a valid serial number')
                else:
                    LOGGER.info('lshw command not found')
                if shutil.which('hostnamectl') is not None:
                    result = subprocess.run(['hostnamectl', 'status'], capture_output=True, text=True, check=False)
                    if result.returncode == 0:
                        for line in result.stdout.splitlines():
                            if 'hardware id' in line.lower() or 'serial' in line.lower():
                                serial_part = line.split(':', 1)[1].strip()
                                if serial_part and serial_part != 'None' and (serial_part != 'To be filled by O.E.M.'):
                                    return serial_part
                    LOGGER.info('hostnamectl did not provide a valid serial number')
                else:
                    LOGGER.info('hostnamectl command not found')
                if shutil.which('dmidecode') is None:
                    LOGGER.warning('dmidecode command not found, cannot fetch serial via dmidecode.')
                    return 'Unknown'
                try:
                    result = subprocess.run(['dmidecode', '-s', 'system-serial-number'], capture_output=True, text=True, check=False)
                    if result.returncode == 0:
                        output = result.stdout.strip()
                        if output and output != 'None' and (output != 'To be filled by O.E.M.'):
                            return output
                except Exception as e:
                    LOGGER.info(f'dmidecode without sudo failed: {e}')
            except Exception as e:
                LOGGER.error(f'Exception calling hardware info commands for serial: {e}')
    except Exception as e:
        LOGGER.error(f'General exception in get_serial_number: {e}')
    return serial

def get_screen_resolution(root_tk_instance=None) -> str:
    """Get screen resolution using Tkinter or fallback for headless environments."""
    try:
        import tkinter as tk
        if root_tk_instance:
            width = root_tk_instance.winfo_screenwidth()
            height = root_tk_instance.winfo_screenheight()
            resolution = f'{width}x{height}'
        else:
            temp_root = tk.Tk()
            temp_root.withdraw()
            width = temp_root.winfo_screenwidth()
            height = temp_root.winfo_screenheight()
            resolution = f'{width}x{height}'
            temp_root.destroy()
        return resolution
    except Exception as e_tk:
        if platform.system() == 'Linux':
            try:
                result = subprocess.run(['xrandr'], capture_output=True, text=True, check=True)
                for line in result.stdout.splitlines():
                    if '*' in line:
                        match = re.search('(\\d+x\\d+)', line)
                        if match:
                            return match.group(1)
                return 'N/A (xrandr parse error)'
            except Exception as e_xrandr:
                return f'N/A (tk error: {e_tk!s}, xrandr error: {e_xrandr!s})'
        return f'N/A (tk error: {e_tk!s})'

def get_cpu_info_detailed() -> str:
    """
    Returns a human-friendly CPU info string, e.g.:
    Intel Core i7-1185G7 (11th Gen) @ 3.0GHz (4 cores, 8 threads)
    """
    try:
        cpu_brand = ''
        if cpuinfo is not None:
            cpu_brand = cpuinfo.get_cpu_info().get('brand_raw', '')
        if not cpu_brand:
            cpu_brand = platform.processor()
        if not cpu_brand and platform.system() == 'Linux':
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if line.startswith('model name'):
                            cpu_brand = line.split(':', 1)[1].strip()
                            break
            except Exception:
                pass
        if not cpu_brand or cpu_brand.lower() in ['x86_64', 'amd64']:
            try:
                if platform.system() == 'Linux':
                    try:
                        with open('/proc/cpuinfo', 'r') as f:
                            for line in f:
                                if line.startswith('model name'):
                                    cpu_brand = line.split(':', 1)[1].strip()
                                    break
                    except Exception:
                        pass
                    if not cpu_brand or cpu_brand.lower() in ['x86_64', 'amd64']:
                        process = subprocess.run(['dmidecode', '-t', 'processor'], capture_output=True, text=True, check=False)
                        if process.returncode == 0:
                            for line in process.stdout.splitlines():
                                if 'Version:' in line:
                                    cpu_brand = line.split(':', 1)[1].strip()
                                    break
                    if not cpu_brand or cpu_brand.lower() in ['x86_64', 'amd64']:
                        process = subprocess.run(['lscpu'], capture_output=True, text=True, check=False)
                        if process.returncode == 0:
                            for line in process.stdout.splitlines():
                                if 'Model name:' in line:
                                    cpu_brand = line.split(':', 1)[1].strip()
                                    break
                elif platform.system() == 'Windows':
                    process = subprocess.run(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'cpu', 'get', 'name'], capture_output=True, text=True, check=False, shell=False)
                    if process.returncode == 0:
                        for line in process.stdout.splitlines():
                            if line.strip() and (not line.startswith('Name')):
                                cpu_brand = line.strip()
                                break
                    if not cpu_brand or cpu_brand.lower() in ['x86_64', 'amd64']:
                        process = subprocess.run(['powershell', '-Command', 'Get-CimInstance Win32_Processor | Select-Object -ExpandProperty Name'], capture_output=True, text=True, check=False)
                        if process.returncode == 0 and process.stdout.strip():
                            cpu_brand = process.stdout.strip()
            except Exception as e:
                LOGGER.error(f'Error in CPU detection fallback: {e}')
        if not cpu_brand or cpu_brand.lower() in ['x86_64', 'amd64']:
            return 'Unknown CPU (x86_64)'
        cpu_brand = _format_cpu_brand(cpu_brand)
        try:
            cpu_cores = psutil.cpu_count(logical=False)
            cpu_threads = psutil.cpu_count(logical=True)
        except Exception:
            cpu_cores = cpu_threads = None
        ghz = None
        try:
            freq = psutil.cpu_freq()
            if freq and hasattr(freq, 'max') and (freq.max > 0):
                ghz = freq.max / 1000.0
            elif freq and hasattr(freq, 'current') and (freq.current > 0):
                ghz = freq.current / 1000.0
        except Exception:
            pass
        result = cpu_brand
        if ghz:
            result += f' @ {ghz:.1f}GHz'
        if cpu_cores is not None:
            result += f" ({cpu_cores} core{('s' if cpu_cores > 1 else '')}"
            if cpu_threads is not None and cpu_threads != cpu_cores:
                result += f", {cpu_threads} thread{('s' if cpu_threads > 1 else '')}"
            result += ')'
        return result
    except Exception as e:
        LOGGER.error(f'Error getting CPU info: {e}')
        return 'CPU info unavailable'

def _format_cpu_brand(cpu_brand: str) -> str:
    """Format CPU brand string to a raw model by stripping trademarks and 'Processor'/ 'CPU'."""
    if not cpu_brand:
        return 'Unknown CPU'
    for token in ['(R)', '(TM)', '®', '™']:
        cpu_brand = cpu_brand.replace(token, '')
    cpu_brand = cpu_brand.replace('Processor', '').replace('CPU', '').strip()
    cpu_brand = ' '.join(cpu_brand.split())
    return cpu_brand

def get_gpu_info() -> List[str]:
    """Attempts to detect GPU(s) and returns in format: NVIDIA Quadro A5000 16GB"""
    gpus = []
    try:
        if platform.system() == 'Windows':
            try:
                process = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], capture_output=True, text=True, check=True)
                for line in process.stdout.splitlines():
                    if not line.strip():
                        continue
                    parts = [p.strip() for p in line.split(',')]
                    if len(parts) >= 2 and parts[1].isdigit():
                        name, mem_mb_str = (parts[0], parts[1])
                        mem_gb = round(int(mem_mb_str) / 1024, 1)
                        gpus.append(_format_gpu_name(f'{name} {mem_gb}GB'))
                    else:
                        gpus.append(_format_gpu_name(parts[0]))
                return gpus
            except Exception:
                pass
            ps_command = ['powershell', '-Command', 'Get-CimInstance Win32_VideoController | ForEach-Object { $name = $_.Name.Trim(); $ram = $_.AdapterRAM/1073741824; $ram = [Math]::Round($ram, 1); if ($ram -gt 0) {     Write-Output "$name $ram GB" } else {     Write-Output "$name" } }']
            try:
                process = subprocess.run(ps_command, capture_output=True, text=True, check=True)
                output = process.stdout.strip()
                if output:
                    raw_gpus = [line.strip() for line in output.splitlines() if line.strip()]
                    gpus.extend([_format_gpu_name(gpu) for gpu in raw_gpus])
            except Exception:
                try:
                    process = subprocess.run(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'path', 'Win32_VideoController', 'get', 'Name,AdapterRAM'], capture_output=True, text=True, check=True)
                    for line in process.stdout.splitlines():
                        if line.strip() and (not line.startswith('Name')):
                            parts = line.strip().rsplit(None, 1)
                            if len(parts) == 2 and parts[1].isdigit():
                                ram_gb = round(int(parts[1]) / 1073741824, 1)
                                gpu_name = f'{parts[0]} {ram_gb}GB'
                                gpus.append(_format_gpu_name(gpu_name))
                            else:
                                gpus.append(_format_gpu_name(line.strip()))
                except Exception:
                    gpus.append('Unknown GPU')
        elif platform.system() == 'Linux':
            # First check if lshw is available
            lshw_available = shutil.which('lshw') is not None
            lspci_available = shutil.which('lspci') is not None
            
            if not lshw_available and not lspci_available:
                LOGGER.error('Neither lshw nor lspci commands are available. Please install them with: sudo dnf install lshw pciutils')
                return ['GPU detection unavailable - install lshw and pciutils']
                
            if lshw_available:
                try:
                    process = subprocess.run(['lshw', '-C', 'display', '-json'], capture_output=True, text=True, check=True)
                    try:
                        import json
                        gpu_data = json.loads(process.stdout)
                        if isinstance(gpu_data, dict):
                            gpu_data = [gpu_data]
                        for gpu in gpu_data:
                            name = gpu.get('product', 'Unknown GPU')
                            vendor = gpu.get('vendor', '').replace(',', '')
                            if vendor and (not name.startswith(vendor)):
                                name = f'{vendor} {name}'
                            size = gpu.get('size')
                            if size:
                                size_gb = round(size / 1073741824, 1)
                                name += f' ({size_gb}GB VRAM)'
                            gpus.append(_format_gpu_name(name))
                        if gpus:
                            return gpus
                    except json.JSONDecodeError:
                        LOGGER.warning('Failed to parse lshw JSON output')
                except Exception as e:
                    LOGGER.warning(f'lshw command failed: {e}')
            
            # Use lspci as fallback
            if lspci_available:
                try:
                    process = subprocess.run(['lspci', '-nn'], capture_output=True, text=True, check=True)
                    for line in process.stdout.splitlines():
                        if 'VGA compatible controller' in line or '3D controller' in line:
                            parts = line.split(':', 2)
                            if len(parts) >= 3:
                                gpu_name = parts[2].strip()
                                gpus.append(_format_gpu_name(gpu_name))
                except Exception as e:
                    LOGGER.error(f'lspci command failed: {e}')
            
            if not gpus:
                LOGGER.warning('No GPUs detected')
                gpus.append('Unknown GPU')
        if not gpus:
            gpus.append('No GPU detected')
    except Exception as e:
        LOGGER.error(f'Error getting GPU info: {e}')
        gpus.append(f'GPU detection error: {str(e)}')
    return gpus

def _format_gpu_name(gpu_name: str) -> str:
    """Format GPU name to be more human-readable (e.g. 'NVIDIA Quadro A5000 16GB')."""
    if not gpu_name or gpu_name.lower() in ['unknown gpu', 'no gpu detected']:
        return gpu_name
    gpu_name = gpu_name.replace('Corporation', '').replace('Inc.', '').replace('Ltd.', '')
    gpu_name = gpu_name.replace('(TM)', '').replace('(R)', '').replace('®', '').replace('™', '')
    gpu_name = gpu_name.replace('Graphics', '').replace('Family', '').strip()
    memory_match = re.search('(\\d+)\\s*GB', gpu_name, re.IGNORECASE)
    memory = f' {memory_match.group(1)}GB' if memory_match else ''
    if 'NVIDIA' in gpu_name.upper() or 'GEFORCE' in gpu_name.upper():
        if 'GeForce' in gpu_name:
            match = re.search('GeForce\\s+(RTX|GTX)\\s*(\\d{3,4})\\s*(Ti|SUPER)?', gpu_name, re.IGNORECASE)
            if match:
                series = match.group(1).upper()
                model = match.group(2)
                variant = f' {match.group(3)}' if match.group(3) else ''
                return f'NVIDIA GeForce {series} {model}{variant}{memory}'
        elif 'Quadro' in gpu_name:
            match = re.search('Quadro\\s*([A-Z]?\\d+[A-Z]?)', gpu_name, re.IGNORECASE)
            if match:
                model = match.group(1).upper()
                return f'NVIDIA Quadro {model}{memory}'
        elif 'Tesla' in gpu_name:
            match = re.search('Tesla\\s*([A-Z]?\\d+[A-Z]?)', gpu_name, re.IGNORECASE)
            if match:
                model = match.group(1).upper()
                return f'NVIDIA Tesla {model}{memory}'
        if not gpu_name.startswith('NVIDIA'):
            gpu_name = f'NVIDIA {gpu_name}'
    elif 'AMD' in gpu_name.upper() or 'RADEON' in gpu_name.upper():
        if 'Radeon' in gpu_name:
            match = re.search('Radeon\\s*(RX|HD|R\\d+|Pro)?\\s*(\\d{3,4})\\s*(XT|XTX)?', gpu_name, re.IGNORECASE)
            if match:
                series = f' {match.group(1)}' if match.group(1) else ''
                model = match.group(2)
                variant = f' {match.group(3)}' if match.group(3) else ''
                return f'AMD Radeon{series} {model}{variant}{memory}'
        if not gpu_name.startswith('AMD'):
            gpu_name = f'AMD {gpu_name}'
    elif 'Intel' in gpu_name:
        match = re.search('Intel\\s*(HD|UHD|Iris)\\s*(Pro)?\\s*(\\d{3,4})?', gpu_name, re.IGNORECASE)
        if match:
            series = match.group(1)
            pro = f' {match.group(2)}' if match.group(2) else ''
            model = f' {match.group(3)}' if match.group(3) else ''
            return f'Intel {series}{pro}{model}'
    gpu_name = ' '.join(gpu_name.split())
    words = gpu_name.split()
    formatted_words = []
    for word in words:
        if word.upper() in ['NVIDIA', 'AMD', 'INTEL', 'GTX', 'RTX', 'RX', 'HD', 'UHD', 'GB']:
            formatted_words.append(word.upper())
        elif word.lower() in ['geforce', 'radeon', 'quadro', 'tesla', 'iris']:
            formatted_words.append(word.capitalize())
        else:
            formatted_words.append(word)
    return ' '.join(formatted_words)

def get_battery_info() -> dict:
    """Get battery status and condition information."""
    battery_info = {'present': False, 'percent': None, 'charging': None, 'secsleft': None, 'cycle_count': None, 'health': None, 'status': 'Not detected'}
    try:
        batt = psutil.sensors_battery()
        if batt is not None:
            battery_info['present'] = True
            battery_info['percent'] = round(batt.percent)
            battery_info['charging'] = batt.power_plugged
            battery_info['secsleft'] = batt.secsleft
            battery_info['status'] = 'Charging' if batt.power_plugged else 'Discharging'
        if platform.system() == 'Windows':
            try:
                ps = subprocess.run(['powershell', '-Command', 'Get-WmiObject -Class BatteryStatus -Namespace root\\wmi | Select-Object -Property CycleCount,DesignCapacity,FullChargedCapacity'], capture_output=True, text=True, check=False)
                for line in ps.stdout.splitlines():
                    if 'CycleCount' in line:
                        try:
                            battery_info['cycle_count'] = int(line.split(':')[-1].strip())
                        except Exception:
                            pass
                    if 'DesignCapacity' in line:
                        try:
                            battery_info['design_capacity'] = int(line.split(':')[-1].strip())
                        except Exception:
                            pass
                    if 'FullChargedCapacity' in line:
                        try:
                            battery_info['full_charged_capacity'] = int(line.split(':')[-1].strip())
                        except Exception:
                            pass
                if battery_info.get('design_capacity') and battery_info.get('full_charged_capacity'):
                    health = battery_info['full_charged_capacity'] / battery_info['design_capacity'] * 100
                    battery_info['health'] = round(health, 1)
            except Exception:
                pass
        elif platform.system() == 'Linux':
            try:
                # Modern systems use /sys/class/power_supply
                power_supply_path = '/sys/class/power_supply'
                if os.path.exists(power_supply_path):
                    batteries = [d for d in os.listdir(power_supply_path) if 'BAT' in d]
                    if batteries:
                        bat_path = os.path.join(power_supply_path, batteries[0])
                        def read_bat_file(fname):
                            try:
                                with open(os.path.join(bat_path, fname), 'r') as f:
                                    return f.read().strip()
                            except Exception:
                                return None

                        cycle_count = read_bat_file('cycle_count')
                        if cycle_count:
                            battery_info['cycle_count'] = int(cycle_count)

                        design_cap = read_bat_file('energy_full_design')
                        full_cap = read_bat_file('energy_full')

                        if design_cap and full_cap:
                            # Values are in micro-watt-hours
                            design_cap_mwh = int(design_cap)
                            full_cap_mwh = int(full_cap)
                            if design_cap_mwh > 0:
                                battery_info['design_capacity'] = design_cap_mwh
                                battery_info['full_charged_capacity'] = full_cap_mwh
                                health = (full_cap_mwh / design_cap_mwh) * 100
                                battery_info['health'] = round(health, 1)
            except Exception as e:
                 LOGGER.warning(f"Could not get detailed battery health on Linux from sysfs: {e}")
    except Exception as e:
        LOGGER.error(f"An error occurred in get_battery_info: {e}", exc_info=True)

    return battery_info

def _get_disk_info() -> List[Dict[str, Any]]:
    """Gets disk information, handling different platforms."""
    disks = []
    if platform.system() == 'Windows':
        try:
            # Using PowerShell is more modern and provides structured data
            # Using Get-PhysicalDisk is better than Win32_DiskDrive
            ps_command = 'Get-PhysicalDisk | Select-Object -Property Model, @{Name="SizeGB";Expression={[Math]::Round($_.Size / 1GB, 2)}} | ConvertTo-Json -Compress'
            result = subprocess.run(['powershell', '-Command', ps_command], capture_output=True, text=True, check=True)
            # The output can be a single object or an array of objects
            json_output = result.stdout.strip()
            if json_output:
                if not json_output.startswith('['):
                    json_output = f'[{json_output}]'
                disk_data = json.loads(json_output)

                for disk in disk_data:
                    disks.append({
                        'model': disk.get('Model', 'Unknown').strip(),
                        'size_gb': disk.get('SizeGB', 0)
                    })
        except Exception as e:
            LOGGER.error(f"Failed to get disk info using PowerShell: {e}")
            # Fallback to wmic
            try:
                wmic_command = ['C:\\Windows\\System32\\wbem\\WMIC.exe', 'diskdrive', 'get', 'model,size', '/format:csv']
                result = subprocess.run(wmic_command, capture_output=True, text=True, check=True)
                lines = result.stdout.strip().splitlines()
                if len(lines) > 1:
                    for line in lines[1:]:
                        # WMIC output can be messy, find the last comma
                        parts = line.strip().split(',')
                        if len(parts) > 1 and parts[-1].isdigit():
                            model = ','.join(parts[1:-1]).strip()
                            size_bytes = parts[-1]
                            size_gb = round(int(size_bytes) / (1024**3), 2)
                            disks.append({'model': model, 'size_gb': size_gb})
            except Exception as e_wmic:
                LOGGER.error(f"Failed to get disk info using WMIC: {e_wmic}")
                disks.append({'model': 'Disk info unavailable', 'size_gb': 0})

    elif platform.system() == 'Linux':
        try:
            # Call the centralized detailed drive info function
            disks = get_detailed_drive_info()
            LOGGER.info(f'Retrieved detailed disk info: {len(disks)} items')
        except Exception as e:
            LOGGER.error(f'Error fetching detailed drive info: {e}')
            disks = [{'model': 'Disk info unavailable', 'size_gb': 0, 'smart_passed': None}]
    info['disks'] = disks
    return info

def _system_info_summary(root_tk_instance=None) -> Dict[str, Any]:
    """
    Gathers comprehensive system information.
    Used internally by other functions; not exposed via API.
    """
    LOGGER.info("Gathering system information...")
    try:
        virtual_mem = psutil.virtual_memory()
        total_ram_gb = round(virtual_mem.total / (1024**3), 2)

        info = {
            'timestamp': datetime.datetime.now().isoformat(),
            'platform': f"{platform.system()} {platform.release()}",
            'hostname': platform.node(),
            'serial_number': get_serial_number(),
            'cpu': get_cpu_info_detailed(),
            'ram_gb': total_ram_gb,
            'gpus': get_gpu_info(),
            'disks': _get_disk_info(),
            'screen_resolution': get_screen_resolution(root_tk_instance),
            'battery': get_battery_info(),
            'detailed_drives': []
        }

        all_disks = []
        if platform.system() == 'Linux':
            all_disks = [d.device for d in psutil.disk_partitions()]
        elif platform.system() == 'Windows':
            # On windows, device paths are like 'C:\\'. We need physical drive names.
            # This is complex. Let's skip detailed drive info on windows for now.
            pass

        for device_path in all_disks:
            try:
                detailed_info = get_detailed_drive_info(device_path)
                if detailed_info:
                    info['detailed_drives'].append(detailed_info)
            except Exception as e:
                LOGGER.warning(f"Could not get detailed info for drive {device_path}: {e}")

        LOGGER.info("Successfully gathered system information.")
        return info
    except Exception as e:
        LOGGER.critical(f"A critical error occurred in get_system_info: {e}", exc_info=True)            
        return {'error': f"Failed to retrieve system information: {e}"}


def get_device_type() -> str:
    """
    Attempts to detect if the device is a laptop, desktop, or unknown using OS-specific heuristics.
    Returns: 'laptop', 'desktop', or 'unknown'
    """
    import platform, glob
    system = platform.system()
    if system == 'Windows':
        try:
            output = subprocess.check_output(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'path', 'Win32_Battery', 'get', 'BatteryStatus']).decode(errors='ignore')
            if any((l.strip().isdigit() for l in output.splitlines() if l.strip())):
                return 'laptop'
        except Exception:
            pass
        try:
            output = subprocess.check_output(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'SystemEnclosure', 'get', 'ChassisTypes']).decode(errors='ignore')
            for line in output.splitlines():
                line = line.strip()
                if line.isdigit() and int(line) in [8, 9, 10, 14]:
                    return 'laptop'
        except Exception:
            pass
        try:
            output = subprocess.check_output(['C:\\Windows\\System32\\wbem\\WMIC.exe', 'computersystem', 'get', 'PCSystemType']).decode(errors='ignore')
            for line in output.splitlines():
                line = line.strip()
                if line == '2':
                    return 'laptop'
                elif line == '1':
                    return 'desktop'
        except Exception:
            pass
        return 'unknown'
    elif system == 'Linux':
        try:
            if glob.glob('/sys/class/power_supply/BAT*'):
                return 'laptop'
        except Exception:
            pass
        try:
            with open('/sys/class/dmi/id/chassis_type') as f:
                chassis_type = int(f.read().strip())
                if chassis_type in [8, 9, 10, 14]:
                    return 'laptop'
                elif chassis_type == 3:
                    return 'desktop'
        except Exception:
            pass
        for dmi_file in ['/sys/class/dmi/id/product_name', '/sys/class/dmi/id/product_family']:
            try:
                with open(dmi_file) as f:
                    val = f.read().lower()
                    if 'laptop' in val or 'notebook' in val:
                        return 'laptop'
                    elif 'desktop' in val:
                        return 'desktop'
            except Exception:
                pass
        return 'unknown'
    else:
        return 'unknown'

def get_system_info(root_tk_instance=None) -> Dict[str, Any]:
    """Gathers detailed system information, including make, model, serial, computrace, battery."""
    info = {'hostname': platform.node(), 'os_version': f'{platform.system()} {platform.release()} ({platform.version()})', 'architecture': platform.machine(), 'cpu': get_cpu_info_detailed(), 'memory': 'N/A', 'disks': [], 'gpus': get_gpu_info(), 'screen_resolution': get_screen_resolution(root_tk_instance), 'network_interfaces': [], 'make': 'Unknown', 'model': 'Unknown', 'serial_number': get_serial_number(), 'computrace': 'Unknown', 'battery': get_battery_info()}
    try:
        if platform.system() == 'Linux':
            try:
                make_path = '/sys/class/dmi/id/sys_vendor'
                model_path = '/sys/class/dmi/id/product_name'
                try:
                    with open(make_path, 'r') as f:
                        make = f.read().strip()
                        if make and make != 'None' and (make != 'To be filled by O.E.M.'):
                            info['make'] = make
                except (FileNotFoundError, PermissionError):
                    pass
                try:
                    with open(model_path, 'r') as f:
                        model = f.read().strip()
                        if model and model != 'None' and (model != 'To be filled by O.E.M.'):
                            info['model'] = model
                except (FileNotFoundError, PermissionError):
                    pass
            except Exception as e:
                LOGGER.warning(f'Error getting make/model from /sys files: {e}')
            if info['make'] == 'Unknown' or info['model'] == 'Unknown':
                try:
                    if shutil.which('lshw') is not None:
                        result = subprocess.run(['lshw', '-c', 'system'], capture_output=True, text=True, check=False)
                        if result.returncode == 0:
                            for line in result.stdout.splitlines():
                                if 'vendor:' in line.lower() and info['make'] == 'Unknown':
                                    info['make'] = line.split(':', 1)[1].strip()
                                if 'product:' in line.lower() and info['model'] == 'Unknown':
                                    info['model'] = line.split(':', 1)[1].strip()
                except Exception as e:
                    LOGGER.warning(f'Error getting make/model from lshw: {e}')
            info['serial_number'] = get_serial_number()
            info['computrace'] = 'Unknown (requires elevated privileges)'
        elif platform.system() == 'Windows':
            try:
                make = subprocess.run(['powershell', '-Command', '(Get-WmiObject Win32_ComputerSystem).Manufacturer'], capture_output=True, text=True, check=True)
                info['make'] = make.stdout.strip() or 'Unknown'
            except Exception:
                pass
            try:
                model = subprocess.run(['powershell', '-Command', '(Get-WmiObject Win32_ComputerSystem).Model'], capture_output=True, text=True, check=True)
                info['model'] = model.stdout.strip() or 'Unknown'
            except Exception:
                pass
            try:
                serial = subprocess.run(['powershell', '-Command', '(Get-WmiObject Win32_BIOS).SerialNumber'], capture_output=True, text=True, check=True)
                info['serial_number'] = serial.stdout.strip() or 'Unknown'
            except Exception:
                pass
            try:
                bios = subprocess.run(['powershell', '-Command', 'Get-WmiObject -Class Win32_BIOS | Format-List *'], capture_output=True, text=True, check=True)
                bios_out = bios.stdout.lower()
                if 'computrace' in bios_out or 'absolute' in bios_out:
                    lines = [l for l in bios.stdout.splitlines() if 'computrace' in l.lower() or 'absolute' in l.lower()]
                    info['computrace'] = ', '.join(lines) if lines else 'Present (see BIOS details)'
                else:
                    info['computrace'] = 'Not detected'
            except Exception:
                info['computrace'] = 'Unknown (no access to BIOS info)'
    except Exception:
        pass
    try:
        mem = psutil.virtual_memory()
        info['memory'] = f'{mem.total / 1024 ** 3:.2f} GB (Available: {mem.available / 1024 ** 3:.2f} GB)'
    except Exception as e:
        info['memory'] = f'Error detecting memory: {e}'
    try:
        info['disks'] = get_detailed_drive_info()
    except Exception as e:
        try:
            partitions = psutil.disk_partitions()
            info['disks'] = [p.device for p in partitions] if partitions else []
        except Exception:
            info['disks'] = []
        info['disks_error'] = str(e)
    net_details = []
    try:
        net_if_addrs = psutil.net_if_addrs()
        for interface_name, interface_addresses in net_if_addrs.items():
            for addr in interface_addresses:
                if str(addr.family) == 'AddressFamily.AF_INET':
                    net_details.append(f'{interface_name}: IPv4={addr.address} (Mask={addr.netmask})')
        info['network_interfaces'] = net_details if net_details else ['No network interfaces with IPv4 found.']
    except Exception as e:
        info['network_interfaces'] = [f'Error detecting network interfaces: {e}']
    return info

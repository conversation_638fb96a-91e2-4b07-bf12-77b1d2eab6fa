#!/usr/bin/env python3
"""
Arcoa Nexus Test Runner Script

This script provides a command-line interface for running tests using the test framework.
"""
import argparse
import logging
import os
import sys
import time
import unittest
from typing import Dict, Any, List, Optional, Set

# Make sure we can import our own modules regardless of how we're called
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus # get_available_tests and get_test_suite are no longer primary sources
)
from agent.tests.test_runner import TestRunner
# get_test_suite and get_test_args might be deprecated or need significant rework
# if suites/args are handled differently with unittest discovery.
from agent.tests.test_config import get_setting, get_test_suite, get_test_args


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """
    Set up logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional file to log to
    """
    # Convert string log level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure logging
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            *([] if log_file is None else [logging.FileHandler(log_file)])
        ]
    )


def log_callback(message: str, level: str = "info") -> None:
    """
    Callback function for logging messages from tests.
    
    Args:
        message: The message to log
        level: The log level (info, warning, error, debug)
    """
    logger = logging.getLogger("arcoa.tests")
    log_method = getattr(logger, level.lower(), logger.info)
    log_method(message)


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Arcoa Nexus Test Runner")
    
    # Test selection options
    test_group = parser.add_argument_group("Test Selection")
    test_group.add_argument(
        "--tests",
        nargs="*",
        help="Specific tests to run (if not specified, runs default suite)"
    )
    test_group.add_argument(
        "--suite",
        choices=["default", "quick", "full", "wipe", "stress"],
        default="default",
        help="Predefined test suite to run"
    )
    test_group.add_argument(
        "--categories",
        nargs="*",
        choices=[c.value for c in TestCategory],
        help="Test categories to run"
    )
    test_group.add_argument(
        "--severities",
        nargs="*",
        choices=[s.value for s in TestSeverity],
        help="Test severities to run"
    )
    
    # Execution options
    exec_group = parser.add_argument_group("Execution Options")
    exec_group.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    exec_group.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Maximum number of parallel workers"
    )
    exec_group.add_argument(
        "--report-dir",
        default=get_setting("report_dir"),
        help="Directory to save test reports"
    )
    
    # Output options
    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=get_setting("log_level"),
        help="Logging level"
    )
    output_group.add_argument(
        "--log-file",
        help="Log file path"
    )
    
    # Utility options
    util_group = parser.add_argument_group("Utility Options")
    util_group.add_argument(
        "--list",
        action="store_true",
        help="List available tests and exit"
    )
    util_group.add_argument(
        "--asset",
        help="Asset number or identifier"
    )
    util_group.add_argument(
        "--operator",
        help="Operator ID or name"
    )
    
    return parser.parse_args()


def get_test_metadata_from_test_case(test_case_instance: unittest.TestCase, test_method_name: str) -> Optional[Dict[str, Any]]:
    """
    Retrieves test_metadata from a test method if it exists.
    Assumes the @test decorator in test_framework.py has attached metadata to the wrapper.
    """
    method = getattr(test_case_instance, test_method_name, None)
    if method and hasattr(method, '__func__'): # bound method
        wrapper_func = method.__func__
        if hasattr(wrapper_func, 'test_metadata'):
            return wrapper_func.test_metadata
    return None

def list_available_tests() -> None:
    """List all available tests discoverable by unittest, including metadata if found."""
    loader = unittest.TestLoader()
    # Consistent with previous scope, though 'agent/tests/unit_tests' might also be relevant
    # depending on where @test decorated unittests reside.
    suite = loader.discover(start_dir='agent/tests', pattern='test_*.py')
    
    print("Available tests (from unittest discovery):")
    print("-" * 80)
    
    tests_with_metadata = []

    def extract_tests_from_suite(current_suite):
        if hasattr(current_suite, '__iter__'):
            for item in current_suite:
                if isinstance(item, unittest.TestSuite):
                    extract_tests_from_suite(item) # Recurse for nested suites
                elif isinstance(item, unittest.TestCase):
                    test_method_names = [m for m in dir(item) if m.startswith(loader.testMethodPrefix)]
                    for name in test_method_names:
                        full_test_id = f"{item.__module__}.{item.__class__.__name__}.{name}"
                        metadata = get_test_metadata_from_test_case(item, name)
                        tests_with_metadata.append({
                            "id": full_test_id,
                            "method_name": name,
                            "class_name": item.__class__.__name__,
                            "module_name": item.__module__,
                            "metadata": metadata
                        })
    
    extract_tests_from_suite(suite)
    
    if not tests_with_metadata:
        print("  No tests found matching 'test_*.py' in 'agent/tests/' (or they lack test methods).")
    else:
        # Sort by module, then class, then method name for consistent listing
        sorted_tests = sorted(tests_with_metadata, key=lambda x: (x["module_name"], x["class_name"], x["method_name"]))

        for test_info in sorted_tests:
            print(f"  ID: {test_info['id']}")
            if test_info["metadata"]:
                meta = test_info["metadata"]
                cat = meta.get('category', TestCategory.SYSTEM) # Default if somehow missing
                sev = meta.get('severity', TestSeverity.MEDIUM) # Default if somehow missing
                desc = meta.get('description', 'No description')
                display_name = meta.get('name', test_info['method_name'])
                print(f"    Name: {display_name}")
                # Ensure .value is accessed if they are enums
                print(f"    Category: {cat.value if hasattr(cat, 'value') else cat}")
                print(f"    Severity: {sev.value if hasattr(sev, 'value') else sev}")
                print(f"    Description: {desc}")
            else:
                print(f"    Name: {test_info['method_name']}")
                print(f"    (No category/severity metadata found via @test decorator)")
            print()

    print("\nTest Suites (Note: Suite definitions from the old framework may need updating):")
    print("-" * 80)
    print("  default: (Behavior may differ with unittest discovery and metadata filtering)")
    print("  quick: (Behavior may differ)")
    print("  full: (Behavior may differ)")
    print("  wipe: (Behavior may differ)")
    print("  stress: (Behavior may differ)")


def main() -> int:
    """
    Main entry point for the test runner.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    # Parse command-line arguments
    args = parse_args()
    
    # Set up logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger("arcoa.tests")
    
    # List tests if requested
    if args.list:
        list_available_tests()
        return 0
    
    # Determine which tests to run
    
    loader = unittest.TestLoader()
    discovered_suite = loader.discover(start_dir='agent/tests', pattern='test_*.py')

    # --- Step 1: Collect all discovered tests with their metadata ---
    all_discovered_tests = [] # List of dicts: {"id": str, "metadata": Optional[dict]}
    def collect_all_tests(current_suite):
        if hasattr(current_suite, '__iter__'):
            for item in current_suite:
                if isinstance(item, unittest.TestSuite):
                    collect_all_tests(item) # Recurse
                elif isinstance(item, unittest.TestCase):
                    test_method_names = [m for m in dir(item) if m.startswith(loader.testMethodPrefix)]
                    for name in test_method_names:
                        test_id = f"{item.__module__}.{item.__class__.__name__}.{name}"
                        metadata = get_test_metadata_from_test_case(item, name)
                        all_discovered_tests.append({"id": test_id, "metadata": metadata})
    collect_all_tests(discovered_suite)

    # --- Step 2: Suite Filtering (if --suite is specified) ---
    suite_filtered_tests = []
    if args.suite and args.suite != "default": # Assuming "default" might mean "no specific suite filter" or handled by get_test_suite
        suite_test_names = get_test_suite(args.suite) # List of 'name' strings from metadata
        if not suite_test_names:
            logger.warning(f"Test suite '{args.suite}' is empty or not defined. No tests will be run from this suite.")
            # No need to proceed further if suite is empty
        else:
            logger.info(f"Filtering for test suite: {args.suite} (contains {len(suite_test_names)} test names)")
            for test_info in all_discovered_tests:
                if test_info["metadata"]:
                    # Match against 'name' in metadata (which is func name or @test(name=...))
                    if test_info["metadata"].get("name") in suite_test_names:
                        suite_filtered_tests.append(test_info)
                # else: test without metadata cannot be part of a name-based suite
            if not suite_filtered_tests:
                 logger.warning(f"No discovered tests matched the names specified in suite '{args.suite}'.")
    else:
        # No specific suite, or "default" suite which might mean all tests before other filters
        # For "default" suite, if it means all tests, then all_discovered_tests is the starting point for next filters.
        # If "default" suite from get_test_suite() has specific names, it's handled above.
        # Let's assume if args.suite is "default" and it's not explicitly handled by specific names in get_test_suite,
        # it implies running tests based on other filters (category, severity, specific names).
        # If get_test_suite("default") returns a specific list, it will be handled by the "if args.suite" block.
        # If we want "default" to truly mean "no suite filtering", then:
        if args.suite == "default": # Or if you want "default" to mean "all tests before other filters"
            logger.info("Running with 'default' suite configuration (may apply other filters like category/severity).")
            # If get_test_suite("default") returns specific names, it would have been caught by the "if args.suite and args.suite != 'default'"
            # So, if we reach here with args.suite == "default", it means get_test_suite("default") was not a specific list *or*
            # we are treating "default" as "no specific suite name filtering, proceed to category/severity".
            # For clarity, let's assume if suite_test_names was populated, it was used.
            # If suite_test_names was NOT populated (e.g. suite not found, or args.suite is 'default' and default suite is empty)
            # then we start with all_discovered_tests for the next stage of filtering.
            # This logic seems a bit complex for "default".
            # Simplified: if a specific suite is named (not 'default' for this special handling), filter by it.
            # Otherwise, all discovered tests pass through this stage to category/severity.
             suite_filtered_tests = all_discovered_tests # Pass all tests to next filtering stage
        else: # No suite specified or it was 'default' treated as pass-through
            suite_filtered_tests = all_discovered_tests


    # --- Step 3: Category/Severity Filtering ---
    cat_sev_filtered_tests = []
    filter_categories_args = set(TestCategory(c) for c in args.categories) if args.categories else None
    filter_severities_args = set(TestSeverity(s) for s in args.severities) if args.severities else None

    if filter_categories_args or filter_severities_args:
        for test_info in suite_filtered_tests:
            metadata = test_info["metadata"]
            test_id = test_info["id"]
            if metadata:
                if filter_categories_args:
                    meta_category = metadata.get('category')
                    if not isinstance(meta_category, TestCategory): meta_category = TestCategory(meta_category)
                    if meta_category not in filter_categories_args:
                        logger.debug(f"Skipping {test_id} (post-suite) due to category: {meta_category}")
                        continue
                if filter_severities_args:
                    meta_severity = metadata.get('severity')
                    if not isinstance(meta_severity, TestSeverity): meta_severity = TestSeverity(meta_severity)
                    if meta_severity not in filter_severities_args:
                        logger.debug(f"Skipping {test_id} (post-suite) due to severity: {meta_severity}")
                        continue
            elif filter_categories_args or filter_severities_args: # Filters active, but no metadata
                logger.debug(f"Skipping {test_id} (post-suite) due to no metadata for active cat/sev filters.")
                continue
            cat_sev_filtered_tests.append(test_info)
    else: # No category or severity filters
        cat_sev_filtered_tests = suite_filtered_tests
    
    # --- Step 4: Specific Test ID Filtering (`--tests` argument) ---
    final_tests_to_run_infos = []
    if args.tests:
        # --tests are full test IDs like module.class.method
        user_specified_ids = set(args.tests)
        for test_info in cat_sev_filtered_tests:
            if test_info["id"] in user_specified_ids:
                final_tests_to_run_infos.append(test_info)

        if not final_tests_to_run_infos and args.tests:
             logger.warning(f"Specified tests ({', '.join(args.tests)}) not found after prior filters, or they do not exist.")
    else:
        final_tests_to_run_infos = cat_sev_filtered_tests

    tests_to_run_names = [test_info["id"] for test_info in final_tests_to_run_infos]

    if not tests_to_run_names:
        logger.warning(f"No tests found or selected to run based on current filters (Suite: {args.suite}, Categories: {args.categories}, Severities: {args.severities}, Specific Tests: {args.tests}).")
        return 0

    # Log test plan
    logger.info(f"Running with effective suite: {args.suite if args.suite else 'N/A (all applicable after filters)'}")
    logger.info(f"Effective tests to run ({len(tests_to_run_names)}): {', '.join(tests_to_run_names) if tests_to_run_names else 'None'}")
    if filter_categories_args:
        logger.info(f"Filtering by categories: {', '.join(c.value for c in filter_categories_args)}")
    if filter_severities_args:
        logger.info(f"Filtering by severities: {', '.join(s.value for s in filter_severities_args)}")
    
    # Prepare test arguments
    # test_args will be a dict where keys are full unittest IDs,
    # and values are dicts of arguments for that specific test.
    test_args = {}
    for test_info in final_tests_to_run_infos: # final_tests_to_run_infos contains {"id": ..., "metadata": ...}
        test_id = test_info["id"]
        current_test_specific_args = {"log_callback": log_callback}

        metadata = test_info["metadata"]
        if metadata:
            module_name = metadata.get("module") # This should be like "agent.tests.cpu_test"
            original_func_name = metadata.get("original_func_name") # e.g., "run_basic_cpu_test"

            if module_name and original_func_name:
                # Construct the key as expected by get_test_args in test_config.py
                # Note: In test_framework.py, metadata["module"] was set to just module_name, not full path.
                # And metadata["name"] was set to the test name (possibly custom).
                # We need to ensure 'module' in metadata is the full path like 'agent.tests.cpu_test'
                # or adjust how old_style_key is formed.
                # Based on previous `get_available_tests`, it was `agent.tests.{module_name}.{func_name}`.
                # The `test_id` is `module.class.method`.
                # `metadata['original_func_name']` is the original decorated function name.
                # `metadata['module']` is the module *filename* (e.g., `cpu_test`).
                # We need to reconstruct the key for `get_test_args`.
                # `get_test_args` expects keys like "agent.tests.cpu_test.run_cpu_stress_test".
                # The `test_id` is like "agent.tests.cpu_test.TestCPUFeatures.test_run_basic_cpu_test".
                # The metadata `original_func_name` would be `run_basic_cpu_test`.
                # The module for `test_id` is `item.__module__` which is `agent.tests.cpu_test`.

                # Reconstruct old_style_key using module from test_id and original_func_name from metadata
                # item.__module__ from previous loops would be test_info['module_name'] if we passed it along.
                # Let's assume test_info["metadata"]["module_full_path"] can be constructed or is available.
                # For now, let's try to get it from the test_id's module part if class is involved.

                # Simpler: if original_func_name and its module context are in metadata:
                # The `test_info["module_name"]` (from item.__module__) is the full module path.
                key_for_get_test_args = f"{test_info['module_name']}.{original_func_name}"

                config_file_args = get_test_args(key_for_get_test_args)
                if config_file_args:
                    logger.debug(f"Found config args for {key_for_get_test_args}: {config_file_args}")
                    current_test_specific_args.update(config_file_args)
                else:
                    logger.debug(f"No specific config args found for key: {key_for_get_test_args}")

            else:
                logger.debug(f"Skipping get_test_args for {test_id} due to missing metadata (module or original_func_name).")
        else:
            logger.debug(f"Skipping get_test_args for {test_id} due to no metadata.")

        test_args[test_id] = current_test_specific_args
        logger.debug(f"Final args for {test_id}: {test_args[test_id]}")

    # Create and run the test runner
    start_time = time.time()
    runner = TestRunner(
        log_callback=log_callback,
        report_dir=args.report_dir,
        parallel=args.parallel,
        max_workers=args.workers
    )
    
    # TestRunner.run_tests now expects a list of already filtered test IDs.
    # Category and severity arguments have been removed from its call signature
    # as TestRunner no longer performs this filtering itself.
    results = runner.run_tests(
        tests=tests_to_run_names, # Pass the filtered list of unittest IDs
        test_args=test_args
    )
    
    # Calculate total time
    total_time = time.time() - start_time
    
    # Count results by status
    status_counts = {status.value: 0 for status in TestStatus}
    for result in results.values():
        status = result.get("test_details", {}).get("status", "error")
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Log summary
    logger.info(f"Test run completed in {total_time:.2f} seconds")
    logger.info(f"Total tests: {len(results)}")
    for status, count in status_counts.items():
        if count > 0:
            logger.info(f"  {status}: {count}")
    
    # Determine exit code based on results
    if status_counts.get("fail", 0) > 0 or status_counts.get("error", 0) > 0:
        return 1
    return 0


if __name__ == "__main__":
    sys.exit(main())

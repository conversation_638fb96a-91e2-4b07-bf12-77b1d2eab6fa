"""Command-line interface for the Arcoa Nexus Agent."""
from __future__ import annotations

import argparse
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict

from rich.console import Console

from agent import collector
from agent.client import send_result
from agent.diagnostics import cpu as cpu_diag
from agent.diagnostics.base import registry as diag_registry
from agent.models import AgentPayload

console = Console()
log = logging.getLogger(__name__)


async def main() -> None:  # noqa: C901  (CLI orchestration)
    parser = argparse.ArgumentParser(description="Arcoa Nexus Agent")
    parser.add_argument("--server", help="Server URL (env:NEXUS_SERVER)")
    parser.add_argument("--skip-tests", action="store_true", help="Skip diagnostics and use mock results")
    parser.add_argument("--operator", help="Operator ID/initials (optional)")
    parser.add_argument("--asset", help="Asset number (optional)")
    parser.add_argument(
        "--log-level",
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Logging verbosity (default: info)",
    )
    parser.add_argument("--only", help="Comma-separated diagnostic names to run (default: all)")

    # Defaults from environment variables
    env_server = os.getenv("NEXUS_SERVER")
    env_operator = os.getenv("NEXUS_OPERATOR")
    env_asset = os.getenv("NEXUS_ASSET")

    if not env_server and not parser.parse_known_args()[0].server:
        parser.error("--server or NEXUS_SERVER env var must be provided")

    args = parser.parse_args()

    # Fill arguments with env defaults if missing
    if not args.server:
        args.server = env_server
    if not args.operator and env_operator:
        args.operator = env_operator
    if not args.asset and env_asset:
        args.asset = env_asset

    # Configure root logger based on CLI flag before any further work
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper(), logging.INFO),
        format="%(asctime)s %(levelname)s %(name)s: %(message)s",
    )

    console.print(
        "[bold blue]====================================[/bold blue]\n"
        "[bold blue]       ARCOA NEXUS AGENT v0.1.0      [/bold blue]\n"
        "[bold blue]====================================[/bold blue]\n"
    )

    operator_id = args.operator or console.input("[bold yellow]Enter operator ID/initials:[/bold yellow] ")
    console.print(f"Operator: [bold]{operator_id}[/bold]\n")

    asset_number = args.asset or console.input("[bold yellow]Enter asset number (e.g. 1091234):[/bold yellow] ")
    console.print(f"Asset number: [bold]{asset_number}[/bold]\n")

    # System information
    console.print("[yellow]Collecting system information...[/yellow]")
    serial = collector.get_serial_number()
    sys_info = collector.get_system_info()

    console.print(f"System serial: {serial}")
    console.print(
        f"CPU: {sys_info['cpu_model']} (" f"{sys_info['cpu_cores']} cores / {sys_info['cpu_threads']} threads)"
    )
    console.print(f"Memory: {sys_info['memory_total_gb']} GB")
    console.print(f"Disks detected: {len(sys_info['disks'])}")

    # Determine which diagnostics to run
    selected_diags = (
        [d.strip() for d in args.only.split(",") if d.strip()]
        if args.only
        else list(diag_registry.keys())
    )
    missing = [d for d in selected_diags if d not in diag_registry]
    if missing:
        console.print(f"[red]Unknown diagnostics: {', '.join(missing)}[/red]")
        sys.exit(1)

    console.print(f"Running diagnostics: {', '.join(selected_diags)}")

    all_results = {}
    for diag_name in selected_diags:
        if args.skip_tests:
            console.print(f"[yellow]{diag_name}: using mock result (--skip-tests)[/yellow]")
            # Simple mock result
            all_results[diag_name] = {
                "status": "pass",
                "data": {},
                "started_at": time.strftime("%Y-%m-%dT%H:%M:%S"),
                "finished_at": time.strftime("%Y-%m-%dT%H:%M:%S"),
            }
            continue
        diag_cls = diag_registry[diag_name]
        console.print(f"[yellow]{diag_name}: running...[/yellow]")
        result_obj = diag_cls().run()
        if hasattr(result_obj, "as_dict"):
            res_dict = result_obj.as_dict()
        else:
            try:
                res_dict = dict(result_obj)
            except TypeError:
                res_dict = {}
        res_dict.update(
            {
                "started_at": getattr(result_obj, "started_at", ""),
                "finished_at": getattr(result_obj, "finished_at", ""),
            }
        )
        all_results[diag_name] = res_dict

    # Assemble payload
    payload = AgentPayload(
        asset_serial=serial,
        asset_number=asset_number,
        operator_id=operator_id,
        test_name="diagnostics",
        status=(
            "fail"
            if any(r.get("status") != "pass" for r in all_results.values())
            else "pass"
        ),
        data={"system_info": sys_info, "diagnostics": all_results},
        started_at=min(r.get("started_at", "") for r in all_results.values()),
        finished_at=max(r.get("finished_at", "") for r in all_results.values()),
    )

    test_payload = payload.as_dict()

    # Send
    success = await send_result(args.server, test_payload)
    if not success:
        fname = Path(f"nexus_result_{serial}_{int(time.time())}.json")
        fname.write_text(json.dumps(test_payload, indent=2))
        console.print(f"[yellow]Result saved to {fname}[/yellow]")

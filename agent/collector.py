"""System information collection utilities used by the Arcoa Nexus Agent."""
from __future__ import annotations

import logging
import platform
import subprocess
from typing import Any, Dict, List

import psutil

log = logging.getLogger(__name__)


def _run_subprocess(cmd: List[str]) -> str | None:
    """Run *cmd* and return stdout with basic error handling."""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError, PermissionError) as exc:
        log.debug("Command %s failed: %s", cmd, exc)
        return None


def get_serial_number() -> str:
    """Return the machine serial number, or a best-effort fallback."""
    serial: str | None = None

    if platform.system() == "Windows":
        serial = _run_subprocess([
            r"C:\\Windows\\System32\\wbem\\WMIC.exe",
            "bios",
            "get",
            "serialnumber",
        ])
        if serial:
            lines = [l.strip() for l in serial.splitlines() if l.strip()]
            if len(lines) >= 2:
                serial = lines[-1]
    else:
        # Linux / Unix-like systems – requires root for dmidecode
        serial = _run_subprocess(["dmidecode", "-s", "system-serial-number"])

    if not serial:
        serial = f"UNKNOWN-{platform.node()}"
    return serial


def get_system_info() -> Dict[str, Any]:
    """Collect high-level system information using *psutil* and *platform*."""
    info: Dict[str, Any] = {
        "hostname": platform.node(),
        "system": platform.system(),
        "release": platform.release(),
        "cpu_model": platform.processor(),
        "cpu_cores": psutil.cpu_count(logical=False) or 0,
        "cpu_threads": psutil.cpu_count(logical=True) or 0,
        "memory_total_gb": round(psutil.virtual_memory().total / (1024 ** 3), 2),
        "platform": platform.platform(),
    }

    # Storage devices
    disks = []
    for part in psutil.disk_partitions(all=True):
        try:
            usage = psutil.disk_usage(part.mountpoint)
            disks.append(
                {
                    "device": part.device,
                    "mountpoint": part.mountpoint,
                    "fstype": part.fstype,
                    "size_gb": round(usage.total / (1024 ** 3), 2) if usage.total else 0,
                }
            )
        except (PermissionError, FileNotFoundError):
            # Some mountpoints are not accessible – ignore them
            continue

    info["disks"] = disks
    return info

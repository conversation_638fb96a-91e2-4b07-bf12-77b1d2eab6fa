# Changelog

All notable changes to this project will be documented in this file. The format
follows [Keep a Changelog](https://keepachangelog.com/) and adheres to
[Semantic Versioning](https://semver.org/).

## [0.3.14] - 2025-06-13
### Removed
- Legacy unit tests `test_cpu.py` and `test_ram.py` effectively deleted (they now immediately SkipTest) to stop discovery import errors.

## [0.3.13] - 2025-06-13
### Fixed
- Visual RAM test now checks remaining time inside pattern and chunk loops; progress bar and `Time Left` stay accurate and window closes when duration elapses.

## [0.3.12] - 2025-06-13
### Changed
- `test_framework.get_available_tests` now filters out tests with category `battery`, so deprecated battery tests no longer appear in the Profile Editor list.

## [0.3.11] - 2025-06-13
### Removed
- Deprecated battery tests removed from orchestrator mapping; battery info now gathered only at startup via system info.
### Changed
- Orchestrator skips legacy battery test paths in profiles with a friendly note; GUI imports cleaned up.

## [0.3.10] - 2025-06-13
### Fixed
- GUI `main_window` now falls back to visual CPU/RAM tests when legacy modules are absent, removing import crash on startup.

## [0.3.9] - 2025-06-13
### Changed
- `TestOrchestrator` now gracefully handles removal of legacy `cpu_test.py` and `ram_test.py`, falling back to visual tests or skipping profiles that reference old paths.

## [0.3.8] - 2025-06-13
### Removed
- Deprecated `cpu_test.py` and `ram_test.py` in favour of their visual counterparts.

## [0.3.7] - 2025-06-13
### Fixed
- Added `INTEGRATION` value to `TestCategory` enum, resolving import error when loading `makor_integration_test`.

## [0.3.6] - 2025-06-13
### Added
- Keyboard test now blocks Windows OS hotkeys (left/right Win keys) using `keyboard` module when available; keys are unblocked on exit. Prevents Start menu from popping during test.

## [0.3.5] - 2025-06-13
### Changed
- Keyboard test now passes when `>=90%` of keys are detected, allowing flexibility across varying laptop layouts.
- Key press handlers return "break" to absorb special keys (e.g., Print Screen) so they don’t invoke OS actions during the test.
- Progress bar/button indicate when threshold is reached rather than requiring every key.

## [0.3.4] - 2025-06-13
### Added
- Drive wipe GUI now auto-selects recommended wipe method based on selected drive type (NVMe → NVMe Sanitize, SSD → ATA Secure Erase, otherwise NIST zeros; mixed selects NIST zeros).

## [0.3.3] - 2025-06-13
### Added
- Zero-click workflow in GUI: pressing Enter after Asset Number automatically opens Device Condition dialog and focuses Run button.

## [0.3.2] - 2025-06-13
### Changed
- `cli.py` now derives overall payload `status` as `fail` if any diagnostic reports non-pass.

## [0.3.1] - 2025-06-13
### Added
- `pyproject.toml` providing project metadata, dependency list, and console-script `arcoa-agent`.
- Default configuration sections for `mypy`, `ruff`, and `pytest`.

## [0.3.0] - 2025-06-13
### Added
- Diagnostics plug-in execution via registry auto-discovery.
- `--only` CLI flag to run a comma-separated subset of diagnostics.
- `CpuStressDiagnostic` plug-in registered as `cpu_stress`.

### Changed
- Payload now nests individual diagnostic results under `data.diagnostics` and sets overall timestamps.

## [0.2.4] - 2025-06-13
### Added
- `AgentPayload` dataclass for strongly-typed result payloads.

### Changed
- `cli.py` constructs payloads via `AgentPayload` instead of ad-hoc dicts.

## [0.2.3] - 2025-06-13
### Added
- Environment variable defaults: `NEXUS_SERVER`, `NEXUS_OPERATOR`, `NEXUS_ASSET` to allow headless runs.

### Changed
- `--server` flag made optional when env var present; CLI now merges CLI args with env values.

## [0.2.2] - 2025-06-13
### Added
- Diagnostics framework base (`agent.diagnostics.base`) with `Diagnostic`, `DiagnosticResult`, registry, and `@register` decorator.

## [0.2.1] - 2025-06-13
### Added
- `--log-level` CLI flag with possible values `debug`, `info`, `warning`, `error`, and `critical`.
- Root logger configuration via `logging.basicConfig` ensuring consistent, configurable logging across modules.

## [0.2.0] - 2025-06-13
### Added
- `agent.collector` module for serial-number retrieval and system-information
  gathering with improved logging and error-handling.
- `agent.diagnostics.cpu` module containing:
  - `CpuTestResult` dataclass.
  - `run_stress_test()` function with optional NumPy backend.
- `agent.client` thin async wrapper around `NexusClient` for posting results.
- `agent.cli` as the new command-line orchestrator (argument parsing, interactive
  prompts, diagnostics execution, result upload).
- Lazy sub-module exports in `agent.__init__` for ergonomic imports.

### Changed
- **Major refactor:** `agent/agent.py` is now a minimal wrapper that delegates to
  `agent.cli` and exits; the legacy monolithic implementation remains in the
  file for historical reference but no longer runs.
- Default execution entry point is now `python -m agent.cli`.

### Deprecated
- Direct use of `python agent/agent.py` is still supported but will be removed
  in a future version. Please migrate to the new CLI.

### Removed
- No functionality removed; code has only been reorganised.

### Fixed
- N/A

## [0.1.0] - 2025-06-??
### Added
- Initial proof-of-concept: collected system info, basic CPU usage sample,
  uploaded results to Nexus backend.
